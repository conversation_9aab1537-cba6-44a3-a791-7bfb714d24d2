#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化算法库界面

提供算法管理的完整UI：
- 算法分类树
- 算法卡片展示
- 算法详情面板
- 搜索和筛选
- 收藏和历史
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
    QStackedWidget, QLabel, QTextEdit, QPushButton,
    QLineEdit, QComboBox, QScrollArea, QFrame,
    QGridLayout, QGroupBox
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QSize, QTimer, QPropertyAnimation,
    QEasingCurve, QRect, QRectF
)
from PyQt5.QtGui import (
    QPainter, QColor, QFont, QIcon, QPixmap,
    QBrush, Q<PERSON><PERSON>, Q<PERSON><PERSON>terPath, QLinearGradient
)
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from loguru import logger

from wirevsion.ui.modern_components import (
    ModernCard, ModernButton, ModernInput,
    ModernProgressBar, THEME_COLORS
)
from wirevsion.ui.modern_table import ModernTable


@dataclass
class AlgorithmInfo:
    """算法信息数据类"""
    id: str
    name: str
    category: str
    description: str
    icon: Optional[QIcon] = None
    version: str = "1.0.0"
    author: str = "WireVision"
    tags: List[str] = None
    parameters: Dict[str, Any] = None
    is_favorite: bool = False
    usage_count: int = 0


class AlgorithmCard(QWidget):
    """算法卡片组件"""
    
    clicked = pyqtSignal(str)  # 算法ID
    favorite_toggled = pyqtSignal(str, bool)  # 算法ID, 是否收藏
    
    def __init__(self, algorithm: AlgorithmInfo, parent=None):
        super().__init__(parent)
        
        self.algorithm = algorithm
        self.is_hovered = False
        
        self.setFixedSize(260, 150) # 尺寸微调
        self.setCursor(Qt.PointingHandCursor)
        
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12) # 内边距调整
        layout.setSpacing(6)
        
        header_layout = QHBoxLayout()
        icon_label = QLabel()
        if self.algorithm.icon:
            icon_label.setPixmap(self.algorithm.icon.pixmap(28, 28)) # 图标尺寸
        else:
            icon_label.setText("🔧") 
            icon_label.setStyleSheet("font-size: 20px;")
        icon_label.setFixedSize(28, 28)
        header_layout.addWidget(icon_label)
        
        self.title_label = QLabel(self.algorithm.name)
        title_text_color = THEME_COLORS["text_title"]
        # 确保标题即使在卡片背景较暗时也清晰
        self.title_label.setStyleSheet(f"QLabel {{ color: {title_text_color}; font-size: 14px; font-weight: 500; }}")
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        
        self.favorite_btn = QPushButton("⭐" if self.algorithm.is_favorite else "☆")
        self.favorite_btn.setFixedSize(28, 28)
        fav_icon_color = THEME_COLORS["warning"]
        fav_hover_bg = QColor(THEME_COLORS['warning']).lighter(160).name(QColor.HexArgb)
        self.favorite_btn.setStyleSheet(f"""
            QPushButton {{ background-color: transparent; border: none; font-size: 18px; color: {fav_icon_color}; }}
            QPushButton:hover {{ background-color: {fav_hover_bg}; border-radius: 4px; }}
        """)
        self.favorite_btn.clicked.connect(self._toggle_favorite)
        header_layout.addWidget(self.favorite_btn)
        layout.addLayout(header_layout)
        
        self.desc_label = QLabel(self.algorithm.description)
        self.desc_label.setWordWrap(True)
        desc_text_color = THEME_COLORS["text_secondary"]
        self.desc_label.setStyleSheet(f"QLabel {{ color: {desc_text_color}; font-size: 12px; }}")
        layout.addWidget(self.desc_label)
        layout.addStretch()
        
        info_layout = QHBoxLayout()
        meta_text_color = THEME_COLORS["text_placeholder"]
        self.version_label = QLabel(f"v{self.algorithm.version}")
        self.version_label.setStyleSheet(f"color: {meta_text_color}; font-size: 11px;")
        info_layout.addWidget(self.version_label)
        info_layout.addStretch()
        self.usage_label = QLabel(f"使用 {self.algorithm.usage_count} 次")
        self.usage_label.setStyleSheet(f"color: {meta_text_color}; font-size: 11px;")
        info_layout.addWidget(self.usage_label)
        layout.addLayout(info_layout)
    
    def _toggle_favorite(self):
        """切换收藏状态"""
        self.algorithm.is_favorite = not self.algorithm.is_favorite
        self.favorite_btn.setText("⭐" if self.algorithm.is_favorite else "☆")
        self.favorite_toggled.emit(self.algorithm.id, self.algorithm.is_favorite)
    
    def paintEvent(self, event):
        """绘制事件 (颜色调整)"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        rect = QRectF(self.rect())
        path = QPainterPath()
        path.addRoundedRect(rect, 8, 8) # 卡片圆角减小

        bg_color = QColor(THEME_COLORS["dark_bg_content"]) 
        border_color = QColor(THEME_COLORS["dark_border_secondary"])
        hover_bg_color = QColor(THEME_COLORS["dark_surface_hover"])

        if self.is_hovered:
            painter.fillPath(path, QBrush(hover_bg_color))
            painter.setPen(QPen(QColor(THEME_COLORS["primary"]), 1.5))
        else:
            painter.fillPath(path, QBrush(bg_color))
            painter.setPen(QPen(border_color, 1))
        painter.drawPath(path)
    
    def enterEvent(self, event):
        """鼠标进入"""
        self.is_hovered = True
        self.update()
    
    def leaveEvent(self, event):
        """鼠标离开"""
        self.is_hovered = False
        self.update()
    
    def mousePressEvent(self, event):
        """鼠标点击"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.algorithm.id)


class AlgorithmDetailPanel(QWidget):
    """算法详情面板"""
    
    run_clicked = pyqtSignal(str)  # 算法ID
    config_clicked = pyqtSignal(str)  # 算法ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.current_algorithm: Optional[AlgorithmInfo] = None
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 基本信息卡片
        self.info_card = ModernCard("算法信息")
        info_layout = QVBoxLayout()
        
        # 名称
        self.name_label = QLabel("请选择算法")
        title_color = THEME_COLORS["text_title"]
        self.name_label.setStyleSheet(f"QLabel {{ color: {title_color}; font-size: 18px; font-weight: 600; margin-bottom: 5px; }}")
        info_layout.addWidget(self.name_label)
        
        # 描述
        self.desc_label = QLabel("")
        desc_color = THEME_COLORS["text_primary"]
        self.desc_label.setWordWrap(True)
        text_primary_color = THEME_COLORS["text_primary"]
        self.desc_label.setStyleSheet(f"""
            QLabel {{
                color: {text_primary_color};
                font-size: 14px;
                line-height: 1.5;
            }}
        """)
        info_layout.addWidget(self.desc_label)
        
        # 元信息
        self.meta_label = QLabel("")
        text_secondary_color = THEME_COLORS["text_secondary"]
        self.meta_label.setStyleSheet(f"""
            QLabel {{
                color: {text_secondary_color};
                font-size: 12px;
            }}
        """)
        info_layout.addWidget(self.meta_label)
        
        # 标签
        self.tags_widget = QWidget()
        self.tags_layout = QHBoxLayout(self.tags_widget)
        self.tags_layout.setContentsMargins(0, 0, 0, 0)
        self.tags_layout.setSpacing(8)
        info_layout.addWidget(self.tags_widget)
        
        self.info_card.content_layout.addLayout(info_layout)
        layout.addWidget(self.info_card)
        
        # 参数预览卡片
        self.params_card = ModernCard("参数配置")
        self.params_text = QTextEdit()
        self.params_text.setReadOnly(True)
        params_text_bg_color = THEME_COLORS["dark_bg_input"]
        dark_border_color = THEME_COLORS["dark_border_primary"]
        self.params_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {params_text_bg_color};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {dark_border_color};
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
            }}
        """)
        self.params_text.setMaximumHeight(200)
        self.params_card.add_content(self.params_text)
        layout.addWidget(self.params_card)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.config_btn = ModernButton("配置参数", ModernButton.SECONDARY)
        self.config_btn.clicked.connect(self._on_config_clicked)
        button_layout.addWidget(self.config_btn)
        
        self.run_btn = ModernButton("运行算法", ModernButton.PRIMARY)
        self.run_btn.clicked.connect(self._on_run_clicked)
        button_layout.addWidget(self.run_btn)
        
        layout.addLayout(button_layout)
        
        layout.addStretch()
    
    def set_algorithm(self, algorithm: AlgorithmInfo):
        """设置显示的算法"""
        self.current_algorithm = algorithm
        
        # 更新显示
        self.name_label.setText(algorithm.name)
        self.desc_label.setText(algorithm.description)
        self.meta_label.setText(
            f"版本: {algorithm.version} | 作者: {algorithm.author} | "
            f"分类: {algorithm.category}"
        )
        
        # 更新标签
        # 清除旧标签
        while self.tags_layout.count():
            item = self.tags_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 添加新标签
        if algorithm.tags:
            tag_bg_color = THEME_COLORS["primary"]
            tag_text_color = THEME_COLORS["text_on_primary_bg"]
            for tag in algorithm.tags:
                tag_label = QLabel(tag)
                tag_label.setStyleSheet(f"""
                    QLabel {{
                        background-color: {tag_bg_color};
                        color: {tag_text_color};
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 12px;
                    }}
                """)
                self.tags_layout.addWidget(tag_label)
        
        self.tags_layout.addStretch()
        
        # 更新参数
        if algorithm.parameters:
            import json
            params_text = json.dumps(algorithm.parameters, indent=2, ensure_ascii=False)
            self.params_text.setText(params_text)
        else:
            self.params_text.setText("无参数配置")
        
        # 启用按钮
        self.config_btn.setEnabled(True)
        self.run_btn.setEnabled(True)
    
    def _on_config_clicked(self):
        """配置按钮点击"""
        if self.current_algorithm:
            self.config_clicked.emit(self.current_algorithm.id)
    
    def _on_run_clicked(self):
        """运行按钮点击"""
        if self.current_algorithm:
            self.run_clicked.emit(self.current_algorithm.id)


class ModernAlgorithmLibrary(QWidget):
    """现代化算法库主界面"""
    
    algorithm_selected = pyqtSignal(str)  # 算法ID
    algorithm_run = pyqtSignal(str)  # 算法ID
    algorithm_config = pyqtSignal(str)  # 算法ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.algorithms: Dict[str, AlgorithmInfo] = {}
        self.filtered_algorithms: List[AlgorithmInfo] = []
        self.current_category = "all"
        self.search_text = ""
        
        self._setup_ui()
        self._load_algorithms()
        
        logger.info("现代化算法库界面初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 主内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧分类树
        self.category_tree = self._create_category_tree()
        content_splitter.addWidget(self.category_tree)
        
        # 中间算法列表
        self.algorithm_list = self._create_algorithm_list()
        content_splitter.addWidget(self.algorithm_list)
        
        # 右侧详情面板
        self.detail_panel = AlgorithmDetailPanel()
        self.detail_panel.run_clicked.connect(self.algorithm_run.emit)
        self.detail_panel.config_clicked.connect(self.algorithm_config.emit)
        content_splitter.addWidget(self.detail_panel)
        
        # 设置分割比例
        content_splitter.setSizes([200, 600, 400])
        
        layout.addWidget(content_splitter)
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(60)
        dark_bg_toolbar_color = THEME_COLORS["dark_bg_input"]
        dark_border_color = THEME_COLORS["dark_border_primary"]
        toolbar.setStyleSheet(f"""
            QWidget {{
                background-color: {dark_bg_toolbar_color};
                border-bottom: 1px solid {dark_border_color};
            }}
        """)
        
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(16, 0, 16, 0)
        
        # 搜索框
        self.search_input = ModernInput("搜索算法...", "")
        self.search_input.setFixedWidth(400)
        search_input_bg_color = THEME_COLORS["dark_bg_input"]
        text_primary_color = THEME_COLORS["text_primary"]
        search_input_border_color = THEME_COLORS["dark_border_primary"]
        primary_color = THEME_COLORS["primary"]
        self.search_input.input.setStyleSheet(f""" 
            QLineEdit {{
                background-color: {search_input_bg_color};
                color: {text_primary_color};
                border: 1px solid {search_input_border_color};
                padding: 8px 12px;
                border-radius: 4px; 
            }}
            QLineEdit:focus {{
                border: 1px solid {primary_color};
            }}
        """)
        self.search_input.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_input)
        
        layout.addStretch()
        
        # 视图切换
        self.view_combo = QComboBox()
        self.view_combo.addItems(["卡片视图", "列表视图"])
        dark_surface_hover_color = THEME_COLORS["dark_surface_hover"]
        self.view_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {dark_surface_hover_color};
                color: {text_primary_color};
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 120px;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
             QComboBox::down-arrow {{
                image: url(resources/icons/arrow_down_light.png); /* 确保有浅色图标 */
             }}
        """)
        self.view_combo.currentTextChanged.connect(self._on_view_changed)
        layout.addWidget(self.view_combo)
        
        # 筛选按钮
        filter_btn = ModernButton("筛选", ModernButton.SECONDARY)
        filter_btn.clicked.connect(self._show_filter_menu)
        layout.addWidget(filter_btn)
        
        return toolbar
    
    def _create_category_tree(self) -> QWidget:
        """创建分类树"""
        tree = QTreeWidget()
        tree.setHeaderHidden(True)
        category_tree_bg_color = THEME_COLORS["dark_bg_sidebar"]
        text_primary_color = THEME_COLORS["text_primary"]
        dark_surface_hover_color = THEME_COLORS["dark_surface_hover"]
        primary_color = THEME_COLORS["primary"]
        text_on_primary_selected_color = THEME_COLORS["text_on_primary_bg"]
        tree.setStyleSheet(f"""
            QTreeWidget {{
                background-color: {category_tree_bg_color};
                border: none;
                outline: none;
            }}
            QTreeWidget::item {{
                color: {text_primary_color};
                padding: 8px;
            }}
            QTreeWidget::item:hover {{
                background-color: {dark_surface_hover_color};
            }}
            QTreeWidget::item:selected {{
                background-color: {primary_color};
                color: {text_on_primary_selected_color}; /* 选中项文本颜色 */
            }}
        """)
        
        # 添加分类
        categories = {
            "全部算法": {
                "图像源": ["相机输入", "文件输入", "视频输入"],
                "图像处理": ["滤波", "增强", "变换", "形态学"],
                "特征检测": ["边缘", "角点", "轮廓", "模板匹配"],
                "目标检测": ["颜色", "形状", "文本", "条码"],
                "深度学习": ["分类", "检测", "分割"],
                "测量分析": ["尺寸", "角度", "面积", "统计"],
                "位置校正": ["标定", "配准", "变换"]
            }
        }
        
        for root_name, subcategories in categories.items():
            root_item = QTreeWidgetItem(tree, [root_name])
            root_item.setExpanded(True)
            
            for category, items in subcategories.items():
                category_item = QTreeWidgetItem(root_item, [category])
                
                for item in items:
                    QTreeWidgetItem(category_item, [item])
        
        # 连接信号
        tree.itemClicked.connect(self._on_category_clicked)
        
        return tree
    
    def _create_algorithm_list(self) -> QWidget:
        """创建算法列表"""
        container = QWidget()
        layout = QVBoxLayout(container)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        algorithm_list_bg_color = THEME_COLORS["dark_bg_app"]
        dark_border_color = THEME_COLORS["dark_border_primary"]
        secondary_color = THEME_COLORS["secondary"]
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {algorithm_list_bg_color};
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {algorithm_list_bg_color};
                width: 8px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {dark_border_color};
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {secondary_color};
            }}
        """)
        
        # 算法容器
        self.algorithm_container = QWidget()
        self.algorithm_layout = QGridLayout(self.algorithm_container)
        self.algorithm_layout.setSpacing(16)
        
        scroll_area.setWidget(self.algorithm_container)
        layout.addWidget(scroll_area)
        
        return container
    
    def _load_algorithms(self):
        """加载算法数据"""
        # 示例算法数据
        sample_algorithms = [
            AlgorithmInfo(
                id="gaussian_blur",
                name="高斯模糊",
                category="图像处理",
                description="使用高斯核对图像进行平滑处理，有效去除噪声",
                tags=["滤波", "降噪", "平滑"],
                parameters={"kernel_size": 5, "sigma": 1.0}
            ),
            AlgorithmInfo(
                id="edge_detection",
                name="边缘检测",
                category="特征检测",
                description="使用Canny算法检测图像中的边缘",
                tags=["边缘", "特征", "Canny"],
                parameters={"threshold1": 100, "threshold2": 200}
            ),
            AlgorithmInfo(
                id="template_matching",
                name="模板匹配",
                category="特征检测",
                description="在图像中查找与模板相匹配的区域",
                tags=["匹配", "定位", "模板"],
                parameters={"method": "TM_CCOEFF_NORMED", "threshold": 0.8}
            ),
            AlgorithmInfo(
                id="yolo_detection",
                name="YOLO目标检测",
                category="深度学习",
                description="使用YOLO模型进行实时目标检测",
                tags=["深度学习", "检测", "YOLO"],
                parameters={"model": "yolov8n", "confidence": 0.5},
                usage_count=156
            ),
        ]
        
        # 添加到字典
        for algo in sample_algorithms:
            self.algorithms[algo.id] = algo
        
        # 初始显示所有算法
        self._update_algorithm_display()
    
    def _update_algorithm_display(self):
        """更新算法显示"""
        # 清除现有显示
        while self.algorithm_layout.count():
            item = self.algorithm_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 筛选算法
        self.filtered_algorithms = []
        for algo in self.algorithms.values():
            # 分类筛选
            if self.current_category != "all" and algo.category != self.current_category:
                continue
            
            # 搜索筛选
            if self.search_text:
                search_lower = self.search_text.lower()
                if not any(search_lower in text.lower() for text in 
                          [algo.name, algo.description, algo.category] + (algo.tags or [])):
                    continue
            
            self.filtered_algorithms.append(algo)
        
        # 显示算法卡片
        row, col = 0, 0
        max_cols = 3
        
        for algo in self.filtered_algorithms:
            card = AlgorithmCard(algo)
            card.clicked.connect(self._on_algorithm_clicked)
            card.favorite_toggled.connect(self._on_favorite_toggled)
            
            self.algorithm_layout.addWidget(card, row, col)
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        # 添加弹性空间
        self.algorithm_layout.setRowStretch(row + 1, 1)
    
    def _on_search_changed(self, text: str):
        """搜索文本变化"""
        self.search_text = text
        self._update_algorithm_display()
    
    def _on_category_clicked(self, item: QTreeWidgetItem, column: int):
        """分类点击"""
        category_name = item.text(0)
        
        if category_name == "全部算法":
            self.current_category = "all"
        else:
            # TODO: 根据实际分类结构调整
            self.current_category = category_name
        
        self._update_algorithm_display()
    
    def _on_algorithm_clicked(self, algorithm_id: str):
        """算法卡片点击"""
        if algorithm_id in self.algorithms:
            algorithm = self.algorithms[algorithm_id]
            self.detail_panel.set_algorithm(algorithm)
            self.algorithm_selected.emit(algorithm_id)
    
    def _on_favorite_toggled(self, algorithm_id: str, is_favorite: bool):
        """收藏状态切换"""
        if algorithm_id in self.algorithms:
            self.algorithms[algorithm_id].is_favorite = is_favorite
            logger.debug(f"算法 {algorithm_id} 收藏状态: {is_favorite}")
    
    def _on_view_changed(self, view_type: str):
        """视图类型变化"""
        # TODO: 实现列表视图
        logger.debug(f"切换到视图: {view_type}")
    
    def _show_filter_menu(self):
        """显示筛选菜单"""
        # TODO: 实现筛选菜单
        logger.debug("显示筛选菜单") 