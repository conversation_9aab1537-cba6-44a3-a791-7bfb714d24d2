#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主题管理器

统一管理应用程序的主题和配色方案
"""

from PyQt5.QtGui import QColor
from typing import Dict, Any

class ThemeManager:
    """主题管理器"""
    
    # 深色主题配色方案
    DARK_THEME = {
        # 主要颜色
        "primary": "#007bff",
        "primary_hover": "#0069d9",
        "primary_pressed": "#005cbf",
        "secondary": "#6c757d",
        "secondary_hover": "#5a6268",
        "secondary_pressed": "#545b62",
        "success": "#28a745",
        "success_hover": "#218838",
        "success_pressed": "#1e7e34",
        "warning": "#ffc107",
        "warning_hover": "#e0a800",
        "warning_pressed": "#d39e00",
        "danger": "#dc3545",
        "danger_hover": "#c82333",
        "danger_pressed": "#bd2130",
        
        # 背景色
        "bg_app": "#1e1e1e",
        "bg_sidebar": "#252526",
        "bg_content": "#2a2a2a",
        "bg_header": "#313132",
        "bg_card": "#2d2d30",
        "bg_input": "#3c3c3c",
        "bg_hover": "#3a3a3c",
        "bg_selected": "#0052cc",
        
        # 边框色
        "border_primary": "#444444",
        "border_secondary": "#383838",
        "border_focus": "#007bff",
        
        # 文本色
        "text_primary": "#E0E0E0",
        "text_secondary": "#B0B0B0",
        "text_placeholder": "#888888",
        "text_disabled": "#666666",
        "text_on_primary": "#FFFFFF",
        "text_on_warning": "#212529",
        
        # 网格和辅助线
        "grid_major": "#383838",
        "grid_minor": "#2a2a2a",
        
        # 节点颜色
        "node_input": "#28a745",
        "node_output": "#007bff",
        "node_process": "#ffc107",
        "node_border": "#555555",
        "node_port": "#FFFFFF",
        "node_port_border": "#666666",
        
        # 连接线颜色
        "connection_normal": "#007bff",
        "connection_hover": "#0069d9",
        "connection_selected": "#00ff00",
        "connection_preview": "#888888",
    }
    
    # 当前主题
    _current_theme = DARK_THEME
    
    @classmethod
    def get_color(cls, key: str) -> str:
        """获取颜色值"""
        return cls._current_theme.get(key, "#FFFFFF")
    
    @classmethod
    def get_qcolor(cls, key: str) -> QColor:
        """获取QColor对象"""
        return QColor(cls.get_color(key))
    
    @classmethod
    def apply_theme_to_widget(cls, widget, style_template: str):
        """应用主题到控件"""
        # 替换样式模板中的颜色占位符
        style = style_template
        for key, value in cls._current_theme.items():
            style = style.replace(f"{{{key}}}", value)
        widget.setStyleSheet(style)
    
    @classmethod
    def get_node_style(cls, node_type: str) -> Dict[str, str]:
        """获取节点样式"""
        styles = {
            "input": {
                "bg": cls.get_color("node_input"),
                "border": cls.get_qcolor("node_input").darker(120).name(),
                "text": cls.get_color("text_on_primary")
            },
            "output": {
                "bg": cls.get_color("node_output"),
                "border": cls.get_qcolor("node_output").darker(120).name(),
                "text": cls.get_color("text_on_primary")
            },
            "process": {
                "bg": cls.get_color("node_process"),
                "border": cls.get_qcolor("node_process").darker(130).name(),
                "text": cls.get_color("text_on_warning")
            }
        }
        return styles.get(node_type, styles["process"])
    
    @classmethod
    def get_global_stylesheet(cls) -> str:
        """获取全局样式表"""
        return f"""
        QWidget {{
            color: {cls.get_color("text_primary")};
            font-family: "Segoe UI", "Arial", sans-serif;
        }}
        
        QMainWindow, QDialog {{
            background-color: {cls.get_color("bg_app")};
        }}
        
        QToolTip {{
            background-color: {cls.get_color("bg_input")};
            color: {cls.get_color("text_primary")};
            border: 1px solid {cls.get_color("border_primary")};
            padding: 5px;
            border-radius: 4px;
        }}
        
        QMenu {{
            background-color: {cls.get_color("bg_content")};
            color: {cls.get_color("text_primary")};
            border: 1px solid {cls.get_color("border_primary")};
            padding: 5px;
            border-radius: 6px;
        }}
        
        QMenu::item {{
            padding: 8px 24px;
            border-radius: 4px;
        }}
        
        QMenu::item:selected {{
            background-color: {cls.get_color("bg_selected")};
            color: {cls.get_color("text_on_primary")};
        }}
        
        QScrollBar:vertical {{
            background: {cls.get_color("bg_sidebar")};
            width: 10px;
            border-radius: 5px;
        }}
        
        QScrollBar::handle:vertical {{
            background: {cls.get_color("border_primary")};
            min-height: 25px;
            border-radius: 5px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: {cls.get_color("secondary")};
        }}
        """ 