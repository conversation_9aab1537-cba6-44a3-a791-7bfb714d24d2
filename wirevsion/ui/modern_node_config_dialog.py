#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化节点配置对话框

提供完整的节点配置功能：
- 算法参数设置
- ROI区域配置和绘制
- 输入输出设置
- 显示选项配置
- 实时预览
- 参数模板
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QSlider, QGroupBox,
    QTextEdit, QScrollArea, QFrame, QSizePolicy, QMessageBox,
    QApplication, QSplitter, QListWidget, QListWidgetItem,
    QGraphicsView, QGraphicsScene, QGraphicsRectItem, QGraphicsEllipseItem,
    QGraphicsTextItem, QFileDialog, QProgressBar, QToolButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QRectF, QPointF, QThread, pyqtSlot
from PyQt5.QtGui import (
    QFont, QIcon, QPixmap, QPainter, QColor, QPen, QBrush,
    QCursor, QMouseEvent, QPainterPath
)
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger
import json
import os

from wirevsion.ui.modern_components import ModernCard, ModernButton, THEME_COLORS

# 尝试导入算法相关模块，如果失败则使用简化版本
try:
    from wirevsion.ui.algorithm_ui_manager import AlgorithmUIManager
except ImportError:
    AlgorithmUIManager = None

try:
    from wirevsion.algorithms.registry import AlgorithmRegistry
except ImportError:
    AlgorithmRegistry = None

try:
    from wirevsion.ui.algorithm_config_widgets import AlgorithmConfigWidgetFactory
except ImportError:
    AlgorithmConfigWidgetFactory = None


class ROIDrawingView(QGraphicsView):
    """ROI绘制视图"""

    roi_created = pyqtSignal(QRectF)  # ROI创建信号
    roi_selected = pyqtSignal(int)    # ROI选择信号

    def __init__(self, parent=None):
        super().__init__(parent)

        self.scene = QGraphicsScene()
        self.setScene(self.scene)

        # 绘制状态
        self.drawing_mode = False
        self.start_point = None
        self.current_rect = None
        self.roi_items = []

        # 设置视图属性
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)

        # 样式
        self.setStyleSheet(f"""
            QGraphicsView {{
                background-color: {THEME_COLORS["dark_bg_card"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
            }}
        """)

    def set_image(self, pixmap: QPixmap):
        """设置背景图像"""
        self.scene.clear()
        self.roi_items.clear()

        # 保存当前图像
        self.current_pixmap = pixmap

        if pixmap and not pixmap.isNull():
            self.scene.addPixmap(pixmap)
            self.scene.setSceneRect(QRectF(pixmap.rect()))
            self.fitInView(self.scene.itemsBoundingRect(), Qt.KeepAspectRatio)

    def get_current_image(self):
        """获取当前图像"""
        return getattr(self, 'current_pixmap', None)

    def start_roi_drawing(self):
        """开始ROI绘制模式"""
        self.drawing_mode = True
        self.setCursor(Qt.CrossCursor)
        self.setDragMode(QGraphicsView.NoDrag)

    def stop_roi_drawing(self):
        """停止ROI绘制模式"""
        self.drawing_mode = False
        self.setCursor(Qt.ArrowCursor)
        self.setDragMode(QGraphicsView.RubberBandDrag)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton:
            self.start_point = self.mapToScene(event.pos())
            self.current_rect = None
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing_mode and self.start_point:
            current_point = self.mapToScene(event.pos())

            # 移除之前的临时矩形
            if self.current_rect:
                self.scene.removeItem(self.current_rect)

            # 创建新的临时矩形
            rect = QRectF(self.start_point, current_point).normalized()
            self.current_rect = self.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["primary"]), 2),
                QBrush(QColor(THEME_COLORS["primary"]).lighter(150))
            )
            self.current_rect.setOpacity(0.3)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton and self.start_point:
            end_point = self.mapToScene(event.pos())
            rect = QRectF(self.start_point, end_point).normalized()

            # 检查矩形大小
            if rect.width() > 10 and rect.height() > 10:
                # 移除临时矩形
                if self.current_rect:
                    self.scene.removeItem(self.current_rect)

                # 创建正式的ROI
                roi_rect = self.scene.addRect(
                    rect,
                    QPen(QColor(THEME_COLORS["success"]), 2),
                    QBrush(QColor(THEME_COLORS["success"]).lighter(150))
                )
                roi_rect.setOpacity(0.5)
                self.roi_items.append(roi_rect)

                # 发射信号
                self.roi_created.emit(rect)

            self.start_point = None
            self.current_rect = None
        else:
            super().mouseReleaseEvent(event)


class ModernNodeConfigDialog(QDialog):
    """现代化节点配置对话框"""

    config_changed = pyqtSignal(dict)  # 配置改变信号

    def __init__(self, node, parent=None):
        super().__init__(parent)

        self.node = node
        self.node_id = node.node_id
        self.node_type = node.node_type
        self.node_title = node.title

        # 算法UI管理器
        self.algorithm_ui_manager = AlgorithmUIManager() if AlgorithmUIManager else None
        self.algorithm_registry = AlgorithmRegistry() if AlgorithmRegistry else None

        # 配置数据
        self.config_data = self._get_default_config()
        self.roi_regions = []

        self._setup_ui()
        self._load_algorithm_config()

        logger.info(f"创建现代化节点配置对话框: {self.node_id}")

    def _setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(f"配置节点 - {self.node_title}")
        self.setModal(True)
        self.resize(1000, 700)

        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {THEME_COLORS["dark_bg_app"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
            }}
        """)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 标题栏
        self._create_title_bar(layout)

        # 内容区域
        self._create_content_area(layout)

        # 按钮栏
        self._create_button_bar(layout)

    def _create_title_bar(self, layout):
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["primary"]};
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)

        # 标题
        title = QLabel(f"配置节点 - {self.node_title}")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary_bg"]};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        title_layout.addWidget(title)

        # 节点类型标签
        type_label = QLabel(f"类型: {self.node_type}")
        type_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary_bg"]};
                font-size: 12px;
                opacity: 0.8;
            }}
        """)
        title_layout.addWidget(type_label)

        title_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)

        layout.addWidget(title_bar)

    def _create_content_area(self, layout):
        """创建内容区域"""
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 创建响应式分割器
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setHandleWidth(2)
        self.main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {THEME_COLORS["dark_border_primary"]};
            }}
            QSplitter::handle:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """)

        # 左侧：参数配置面板
        self._create_parameter_panel(self.main_splitter)

        # 右侧：预览面板
        self._create_preview_panel(self.main_splitter)

        # 设置初始分割比例 (40% : 60%)
        self.main_splitter.setSizes([400, 600])

        # 设置最小尺寸
        self.main_splitter.setChildrenCollapsible(False)

        content_layout.addWidget(self.main_splitter)
        layout.addWidget(content, 1)

        # 连接窗口大小变化事件
        self.original_resize_event = self.resizeEvent
        self.resizeEvent = self._on_resize_event

    def _create_parameter_panel(self, splitter):
        """创建参数配置面板"""
        # 创建主参数容器
        self.param_main_widget = QWidget()
        self.param_main_widget.setMinimumWidth(350)
        self.param_main_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border-right: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        param_layout = QVBoxLayout(self.param_main_widget)
        param_layout.setContentsMargins(0, 0, 0, 0)
        param_layout.setSpacing(0)

        # 创建算法配置区域（直接渲染，无选项卡）
        self._create_algorithm_config_area(param_layout)

        # 创建ROI配置区域
        self._create_roi_config_area(param_layout)

        splitter.addWidget(self.param_main_widget)

    def _create_algorithm_config_area(self, layout):
        """创建算法配置区域"""
        # 算法配置标题
        algo_header = QWidget()
        algo_header.setFixedHeight(50)
        algo_header.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                border-bottom: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        header_layout = QHBoxLayout(algo_header)
        header_layout.setContentsMargins(15, 0, 15, 0)

        title_label = QLabel("⚙️ 算法配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addWidget(algo_header)

        # 算法选择区域
        algo_select_widget = QWidget()
        algo_select_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border-bottom: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
        """)

        select_layout = QVBoxLayout(algo_select_widget)
        select_layout.setContentsMargins(15, 15, 15, 15)

        # 算法选择下拉框
        select_label = QLabel("选择算法:")
        select_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_secondary"]};
                font-size: 12px;
                margin-bottom: 5px;
            }}
        """)
        select_layout.addWidget(select_label)

        self.algorithm_combo = QComboBox()
        self.algorithm_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 25px;
            }}
            QComboBox:focus {{
                border-color: {THEME_COLORS["primary"]};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {THEME_COLORS["text_secondary"]};
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                selection-background-color: {THEME_COLORS["primary"]};
            }}
        """)
        self.algorithm_combo.currentTextChanged.connect(self._on_algorithm_changed)
        select_layout.addWidget(self.algorithm_combo)

        layout.addWidget(algo_select_widget)

        # 参数配置容器（滚动区域）
        self.param_scroll_area = QScrollArea()
        self.param_scroll_area.setWidgetResizable(True)
        self.param_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.param_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.param_scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """)

        # 参数配置内容容器
        self.param_content_widget = QWidget()
        self.param_content_layout = QVBoxLayout(self.param_content_widget)
        self.param_content_layout.setContentsMargins(0, 0, 0, 0)
        self.param_content_layout.setSpacing(0)

        self.param_scroll_area.setWidget(self.param_content_widget)
        layout.addWidget(self.param_scroll_area, 1)

    def _create_roi_config_area(self, layout):
        """创建ROI配置区域"""
        # ROI配置标题
        roi_header = QWidget()
        roi_header.setFixedHeight(40)
        roi_header.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                border-top: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-bottom: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        header_layout = QHBoxLayout(roi_header)
        header_layout.setContentsMargins(15, 0, 15, 0)

        title_label = QLabel("📐 ROI区域")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        layout.addWidget(roi_header)

        # ROI列表区域
        roi_list_widget = QWidget()
        roi_list_widget.setFixedHeight(120)
        roi_list_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
            }}
        """)

        roi_layout = QVBoxLayout(roi_list_widget)
        roi_layout.setContentsMargins(15, 10, 15, 10)

        # ROI列表
        self.roi_list = QListWidget()
        self.roi_list.setMaximumHeight(80)
        self.roi_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 2px;
            }}
            QListWidget::item {{
                padding: 4px 8px;
                border-bottom: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
            QListWidget::item:selected {{
                background-color: {THEME_COLORS["primary"]};
                color: {THEME_COLORS["text_on_primary_bg"]};
            }}
            QListWidget::item:hover {{
                background-color: {THEME_COLORS["dark_surface_hover"]};
            }}
        """)
        self.roi_list.itemSelectionChanged.connect(self._on_roi_selection_changed)
        roi_layout.addWidget(self.roi_list)

        layout.addWidget(roi_list_widget)

        # ROI属性配置区域
        roi_props_widget = QWidget()
        roi_props_widget.setFixedHeight(100)
        roi_props_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
            }}
        """)

        props_layout = QVBoxLayout(roi_props_widget)
        props_layout.setContentsMargins(15, 5, 15, 10)

        # ROI属性控件
        props_form_layout = QHBoxLayout()

        # ROI名称
        self.roi_name_edit = QLineEdit()
        self.roi_name_edit.setPlaceholderText("ROI名称")
        self.roi_name_edit.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 12px;
            }}
        """)
        props_form_layout.addWidget(self.roi_name_edit)

        # ROI坐标和尺寸
        self.roi_x_spin = QSpinBox()
        self.roi_x_spin.setRange(0, 9999)
        self.roi_x_spin.setPrefix("X:")
        self.roi_x_spin.setMaximumWidth(60)

        self.roi_y_spin = QSpinBox()
        self.roi_y_spin.setRange(0, 9999)
        self.roi_y_spin.setPrefix("Y:")
        self.roi_y_spin.setMaximumWidth(60)

        self.roi_w_spin = QSpinBox()
        self.roi_w_spin.setRange(1, 9999)
        self.roi_w_spin.setValue(100)
        self.roi_w_spin.setPrefix("W:")
        self.roi_w_spin.setMaximumWidth(70)

        self.roi_h_spin = QSpinBox()
        self.roi_h_spin.setRange(1, 9999)
        self.roi_h_spin.setValue(100)
        self.roi_h_spin.setPrefix("H:")
        self.roi_h_spin.setMaximumWidth(70)

        # 设置坐标控件样式
        for spin in [self.roi_x_spin, self.roi_y_spin, self.roi_w_spin, self.roi_h_spin]:
            spin.setStyleSheet(f"""
                QSpinBox {{
                    background-color: {THEME_COLORS["dark_bg_input"]};
                    color: {THEME_COLORS["text_primary"]};
                    border: 1px solid {THEME_COLORS["dark_border_primary"]};
                    border-radius: 3px;
                    padding: 2px;
                    font-size: 11px;
                }}
            """)

        props_form_layout.addWidget(self.roi_x_spin)
        props_form_layout.addWidget(self.roi_y_spin)
        props_form_layout.addWidget(self.roi_w_spin)
        props_form_layout.addWidget(self.roi_h_spin)

        props_layout.addLayout(props_form_layout)
        layout.addWidget(roi_props_widget)

    def _create_preview_panel(self, splitter):
        """创建预览面板"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(5, 5, 5, 5)

        # 预览标题
        preview_title = QLabel("实时预览")
        preview_title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_title"]};
                font-size: 14px;
                font-weight: bold;
                padding: 5px 0;
            }}
        """)
        preview_layout.addWidget(preview_title)

        # ROI绘制视图
        self.roi_view = ROIDrawingView()
        self.roi_view.roi_created.connect(self._on_roi_created)
        preview_layout.addWidget(self.roi_view, 1)

        # ROI控制按钮
        roi_controls = QWidget()
        roi_controls_layout = QHBoxLayout(roi_controls)
        roi_controls_layout.setContentsMargins(0, 5, 0, 0)

        self.draw_roi_btn = ModernButton("绘制ROI", ModernButton.PRIMARY)
        self.draw_roi_btn.clicked.connect(self._toggle_roi_drawing)
        roi_controls_layout.addWidget(self.draw_roi_btn)

        self.clear_roi_btn = ModernButton("清除ROI", ModernButton.SECONDARY)
        self.clear_roi_btn.clicked.connect(self._clear_roi)
        roi_controls_layout.addWidget(self.clear_roi_btn)

        roi_controls_layout.addStretch()

        preview_layout.addWidget(roi_controls)
        splitter.addWidget(preview_widget)

    def _create_button_bar(self, layout):
        """创建按钮栏"""
        button_bar = QWidget()
        button_bar.setFixedHeight(60)
        button_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        button_layout = QHBoxLayout(button_bar)
        button_layout.setContentsMargins(15, 0, 15, 0)

        # 模板按钮
        template_btn = ModernButton("加载模板", ModernButton.SECONDARY)
        template_btn.clicked.connect(self._load_template)
        button_layout.addWidget(template_btn)

        save_template_btn = ModernButton("保存模板", ModernButton.SECONDARY)
        save_template_btn.clicked.connect(self._save_template)
        button_layout.addWidget(save_template_btn)

        button_layout.addStretch()

        # 主要按钮
        reset_btn = ModernButton("重置", ModernButton.SECONDARY)
        reset_btn.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_btn)

        apply_btn = ModernButton("应用", ModernButton.SUCCESS)
        apply_btn.clicked.connect(self._apply_config)
        button_layout.addWidget(apply_btn)

        ok_btn = ModernButton("确定", ModernButton.PRIMARY)
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = ModernButton("取消", ModernButton.SECONDARY)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addWidget(button_bar)





    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "algorithm": "",
            "parameters": {},
            "roi_regions": [],
            "input_enabled": True,
            "output_enabled": True,
            "display_options": {
                "show_roi": True,
                "show_results": True,
                "overlay_color": "#00FF00"
            }
        }

    def _load_algorithm_config(self):
        """加载算法配置"""
        try:
            # 根据节点类型自动选择算法
            node_type = getattr(self.node, 'node_type', 'unknown')
            node_title = getattr(self.node, 'title', '')

            # 节点类型到算法的映射
            node_algorithm_mapping = {
                'input': {
                    '相机输入': 'image_source.camera',
                    '文件输入': 'image_source.file',
                    '视频输入': 'image_source.video',
                    '网络输入': 'image_source.network'
                },
                'processing': {
                    'Canny边缘': 'image_processing.edge_detection',
                    '高斯模糊': 'image_processing.gaussian_blur',
                    '中值滤波': 'image_processing.median_blur',
                    '双边滤波': 'image_processing.bilateral_filter',
                    '形态学操作': 'image_processing.morphology',
                    '阈值处理': 'image_processing.threshold',
                    '颜色空间转换': 'image_processing.color_space',
                    '直方图处理': 'image_processing.histogram',
                    '对比度调整': 'image_processing.contrast',
                    '降噪处理': 'image_processing.noise_reduction'
                },
                'detection': {
                    '模板匹配': 'feature_detection.template_matching',
                    '角点检测': 'feature_detection.corner_detection',
                    '斑点检测': 'feature_detection.blob_detection',
                    '直线检测': 'feature_detection.line_detection',
                    '圆形检测': 'feature_detection.circle_detection',
                    '轮廓检测': 'feature_detection.contour_detection',
                    '关键点检测': 'feature_detection.keypoint_detection'
                },
                'object_detection': {
                    '颜色检测': 'object_detection.color_detection',
                    '形状检测': 'object_detection.shape_detection',
                    '文本检测': 'object_detection.text_detection',
                    '条码检测': 'object_detection.barcode_detection',
                    '人脸检测': 'object_detection.face_detection'
                },
                'measurement': {
                    '距离测量': 'measurement.distance_measurement',
                    '角度测量': 'measurement.angle_measurement',
                    '面积测量': 'measurement.area_measurement',
                    '几何分析': 'measurement.geometry_analysis',
                    '尺寸测量': 'measurement.dimension_measurement'
                },
                'deep_learning': {
                    'YOLO检测': 'deep_learning.yolo_detection',
                    '图像分类': 'deep_learning.classification',
                    '语义分割': 'deep_learning.segmentation',
                    '姿态估计': 'deep_learning.pose_estimation'
                },
                'position_correction': {
                    '仿射变换': 'position_correction.affine_transform',
                    '透视变换': 'position_correction.perspective_transform',
                    '旋转修正': 'position_correction.rotation_correction',
                    '平移修正': 'position_correction.translation_correction',
                    '缩放修正': 'position_correction.scale_correction'
                }
            }

            # 填充算法下拉框
            self.algorithm_combo.clear()
            self.algorithm_combo.addItem("选择算法...", "")

            # 根据节点类型添加相关算法
            algorithm_count = 0
            selected_algorithm = None

            # 首先尝试根据节点标题直接匹配
            for category, algorithms in node_algorithm_mapping.items():
                if node_title in algorithms:
                    selected_algorithm = algorithms[node_title]
                    display_name = f"{node_title}"
                    self.algorithm_combo.addItem(display_name, selected_algorithm)
                    self.algorithm_combo.setCurrentIndex(1)  # 选择刚添加的算法
                    algorithm_count += 1
                    logger.info(f"根据节点标题自动选择算法: {node_title} -> {selected_algorithm}")
                    break

            # 如果没有直接匹配，添加该类型的所有算法
            if not selected_algorithm and node_type in node_algorithm_mapping:
                for title, algorithm_name in node_algorithm_mapping[node_type].items():
                    display_name = f"{title}"
                    self.algorithm_combo.addItem(display_name, algorithm_name)
                    algorithm_count += 1

            # 如果还是没有找到，添加所有算法
            if algorithm_count == 0:
                for category, algorithms in node_algorithm_mapping.items():
                    for title, algorithm_name in algorithms.items():
                        display_name = f"{title} ({category})"
                        self.algorithm_combo.addItem(display_name, algorithm_name)
                        algorithm_count += 1

            logger.info(f"已加载 {algorithm_count} 个算法到配置对话框")

            # 如果找到了匹配的算法，设置选中状态但不立即加载（避免重复）
            if selected_algorithm:
                # 设置算法选择，这会自动触发 currentIndexChanged 信号
                for i in range(self.algorithm_combo.count()):
                    if self.algorithm_combo.itemData(i) == selected_algorithm:
                        self.algorithm_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            logger.error(f"加载算法配置失败: {e}")
            # 添加一些默认算法选项
            self.algorithm_combo.addItem("相机输入", "image_source.camera")
            self.algorithm_combo.addItem("边缘检测", "image_processing.edge_detection")
            self.algorithm_combo.addItem("高斯模糊", "image_processing.gaussian_blur")

    def _on_algorithm_changed(self, algorithm_display_name: str):
        """算法选择改变事件"""
        algorithm_name = self.algorithm_combo.currentData()

        if not algorithm_name:
            return

        try:
            # 清除之前的参数界面
            self._clear_current_algorithm_ui()

            # 使用新的算法配置系统
            if AlgorithmConfigWidgetFactory:
                algorithm_widget = AlgorithmConfigWidgetFactory.create_config_widget(algorithm_name, self)

                if algorithm_widget:
                    # 设置可用的输入源
                    input_sources = self._get_available_input_sources()
                    algorithm_widget.set_input_sources(input_sources)

                    # 添加到新的参数配置容器
                    self.param_content_layout.addWidget(algorithm_widget)
                    self.param_content_layout.addStretch()
                    self.current_algorithm_ui = algorithm_widget

                    # 连接参数改变信号
                    algorithm_widget.parameter_changed.connect(self._on_parameter_changed)
                    algorithm_widget.preview_requested.connect(self._on_preview_requested)

                    logger.info(f"已加载专用算法配置界面: {algorithm_name}")
                else:
                    # 创建简单的参数界面
                    self._create_simple_parameter_ui(algorithm_name)
            else:
                # 创建简单的参数界面
                self._create_simple_parameter_ui(algorithm_name)

        except Exception as e:
            logger.error(f"切换算法失败: {e}")
            QMessageBox.warning(self, "错误", f"切换算法失败: {str(e)}")

    def _clear_current_algorithm_ui(self):
        """清除当前算法配置界面"""
        if hasattr(self, 'current_algorithm_ui') and self.current_algorithm_ui:
            self.current_algorithm_ui.setParent(None)
            self.current_algorithm_ui.deleteLater()
            self.current_algorithm_ui = None

        # 清空参数配置容器
        for i in reversed(range(self.param_content_layout.count())):
            child = self.param_content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                child.deleteLater()

    def _on_resize_event(self, event):
        """窗口大小变化事件"""
        if hasattr(self, 'original_resize_event'):
            self.original_resize_event(event)

        # 根据窗口大小调整分割比例
        if hasattr(self, 'main_splitter'):
            total_width = self.width()
            if total_width > 0:
                # 计算合适的分割比例
                left_width = max(350, min(500, int(total_width * 0.4)))
                right_width = total_width - left_width - 10  # 减去分割器宽度

                self.main_splitter.setSizes([left_width, right_width])

    def _get_available_input_sources(self) -> List[Dict[str, Any]]:
        """获取可用的输入源"""
        input_sources = []

        try:
            # 从工作流编辑器获取连接信息
            if hasattr(self, 'workflow_editor') and self.workflow_editor:
                # 获取当前节点的输入连接
                current_node_id = getattr(self.node, 'node_id', None)
                if current_node_id:
                    # 查找连接到当前节点的其他节点
                    for connection in self.workflow_editor.connections:
                        if hasattr(connection, 'end_node') and connection.end_node.node_id == current_node_id:
                            source_node = connection.start_node
                            input_sources.append({
                                'node_id': source_node.node_id,
                                'node_name': getattr(source_node, 'title', source_node.node_id),
                                'output_name': 'image',  # 默认输出名称
                                'data_type': 'image'
                            })

            # 如果没有找到连接的输入源，添加一些默认选项
            if not input_sources:
                input_sources = [
                    {
                        'node_id': 'camera_input',
                        'node_name': '相机输入',
                        'output_name': 'image',
                        'data_type': 'image'
                    },
                    {
                        'node_id': 'file_input',
                        'node_name': '文件输入',
                        'output_name': 'image',
                        'data_type': 'image'
                    }
                ]

        except Exception as e:
            logger.error(f"获取输入源失败: {e}")

        return input_sources

    def _on_preview_requested(self):
        """处理预览请求"""
        try:
            logger.info("算法预览请求")

            if hasattr(self, 'current_algorithm_ui') and self.current_algorithm_ui:
                parameters = self.current_algorithm_ui.get_parameters()
                logger.info(f"当前算法参数: {parameters}")

                # 获取算法名称
                algorithm_name = self.algorithm_combo.currentData()

                # 根据算法类型执行不同的预览逻辑
                if algorithm_name == "image_source.camera":
                    self._preview_camera(parameters)
                elif algorithm_name == "image_processing.edge_detection":
                    self._preview_edge_detection(parameters)
                elif algorithm_name == "image_processing.gaussian_blur":
                    self._preview_gaussian_blur(parameters)
                else:
                    self._preview_generic_algorithm(algorithm_name, parameters)

        except Exception as e:
            logger.error(f"预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"预览失败: {str(e)}")

    def _preview_camera(self, parameters: Dict[str, Any]):
        """预览相机"""
        try:
            import cv2
            from PyQt5.QtGui import QImage

            camera_index = parameters.get("camera_index", 0)
            width = parameters.get("width", 640)
            height = parameters.get("height", 480)

            # 尝试打开相机
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                QMessageBox.warning(self, "相机错误", f"无法打开相机 {camera_index}")
                return

            # 设置分辨率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)

            # 读取一帧
            ret, frame = cap.read()
            cap.release()

            if ret:
                # 转换为QPixmap并显示 - 修复BGR到RGB通道问题
                height, width, _ = frame.shape
                # OpenCV使用BGR格式，需要转换为RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览图像
                self.roi_view.set_image(pixmap)
                logger.info("相机预览更新成功")
            else:
                QMessageBox.warning(self, "相机错误", "无法读取相机图像")

        except Exception as e:
            logger.error(f"相机预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"相机预览失败: {str(e)}")

    def _preview_edge_detection(self, parameters: Dict[str, Any]):
        """预览边缘检测"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage

            # 获取当前预览图像
            current_pixmap = self.roi_view.get_current_image()
            if not current_pixmap:
                self._load_preview_image()
                current_pixmap = self.roi_view.get_current_image()

            if current_pixmap:
                # 转换为OpenCV格式
                q_image = current_pixmap.toImage()
                width = q_image.width()
                height = q_image.height()
                ptr = q_image.bits()
                ptr.setsize(q_image.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                image = cv2.cvtColor(arr, cv2.COLOR_RGBA2BGR)

                # 应用边缘检测
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

                # 应用高斯模糊（如果启用）
                if parameters.get("gaussian_blur", True):
                    blur_kernel = parameters.get("blur_kernel", 5)
                    gray = cv2.GaussianBlur(gray, (blur_kernel, blur_kernel), 0)

                # Canny边缘检测
                low_threshold = parameters.get("low_threshold", 50)
                high_threshold = parameters.get("high_threshold", 150)
                aperture_size = parameters.get("aperture_size", 3)
                l2_gradient = parameters.get("l2_gradient", False)

                edges = cv2.Canny(gray, low_threshold, high_threshold,
                                apertureSize=aperture_size, L2gradient=l2_gradient)

                # 转换回彩色图像用于显示
                edges_color = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)

                # 转换为QPixmap
                height, width, _ = edges_color.shape
                bytes_per_line = 3 * width
                q_image = QImage(edges_color.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览图像
                self.roi_view.set_image(pixmap)
                logger.info("边缘检测预览更新成功")

        except Exception as e:
            logger.error(f"边缘检测预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"边缘检测预览失败: {str(e)}")

    def _preview_gaussian_blur(self, parameters: Dict[str, Any]):
        """预览高斯模糊"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage

            # 获取当前预览图像
            current_pixmap = self.roi_view.get_current_image()
            if not current_pixmap:
                self._load_preview_image()
                current_pixmap = self.roi_view.get_current_image()

            if current_pixmap:
                # 转换为OpenCV格式
                q_image = current_pixmap.toImage()
                width = q_image.width()
                height = q_image.height()
                ptr = q_image.bits()
                ptr.setsize(q_image.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                image = cv2.cvtColor(arr, cv2.COLOR_RGBA2BGR)

                # 应用高斯模糊
                kernel_x = parameters.get("kernel_size_x", 15)
                kernel_y = parameters.get("kernel_size_y", 15)
                sigma_x = parameters.get("sigma_x", 0.0)
                sigma_y = parameters.get("sigma_y", 0.0)

                # 确保核大小为奇数
                kernel_x = kernel_x if kernel_x % 2 == 1 else kernel_x + 1
                kernel_y = kernel_y if kernel_y % 2 == 1 else kernel_y + 1

                blurred = cv2.GaussianBlur(image, (kernel_x, kernel_y), sigma_x, sigmaY=sigma_y)

                # 转换为RGB
                blurred_rgb = cv2.cvtColor(blurred, cv2.COLOR_BGR2RGB)

                # 转换为QPixmap
                height, width, _ = blurred_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(blurred_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览图像
                self.roi_view.set_image(pixmap)
                logger.info("高斯模糊预览更新成功")

        except Exception as e:
            logger.error(f"高斯模糊预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"高斯模糊预览失败: {str(e)}")

    def _preview_generic_algorithm(self, algorithm_name: str, parameters: Dict[str, Any]):
        """通用算法预览"""
        logger.info(f"通用算法预览: {algorithm_name}")
        QMessageBox.information(self, "预览", f"算法 {algorithm_name} 的预览功能正在开发中...")

        # 这里可以添加更多算法的预览逻辑

    def _create_simple_parameter_ui(self, algorithm_name: str):
        """创建简单的参数界面"""
        try:
            # 获取参数模式
            param_schema = {}

            if self.algorithm_registry:
                # 获取算法实例
                algorithm_class = self.algorithm_registry.get_algorithm(algorithm_name)
                if algorithm_class:
                    algorithm = algorithm_class()
                    param_schema = algorithm.get_parameter_schema()

            # 如果没有参数模式，使用默认参数
            if not param_schema:
                param_schema = self._get_default_parameter_schema(algorithm_name)

            # 创建参数控件容器
            param_container = QWidget()
            param_container.setStyleSheet(f"""
                QWidget {{
                    background-color: {THEME_COLORS["dark_bg_content"]};
                    border-radius: 8px;
                    margin: 5px;
                }}
            """)
            container_layout = QVBoxLayout(param_container)
            container_layout.setContentsMargins(15, 15, 15, 15)
            container_layout.setSpacing(10)

            # 创建参数控件
            for param_name, param_config in param_schema.items():
                param_type = param_config.get("type", "string")
                description = param_config.get("description", param_name)
                default_value = param_config.get("default")

                # 创建参数行
                param_row = QWidget()
                param_row.setStyleSheet(f"""
                    QWidget {{
                        background-color: {THEME_COLORS["dark_bg_input"]};
                        border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                        border-radius: 6px;
                        padding: 8px;
                    }}
                """)
                row_layout = QVBoxLayout(param_row)
                row_layout.setContentsMargins(8, 8, 8, 8)
                row_layout.setSpacing(5)

                # 参数标签
                label = QLabel(description)
                label.setStyleSheet(f"""
                    QLabel {{
                        color: {THEME_COLORS["text_secondary"]};
                        font-size: 12px;
                        font-weight: bold;
                        background: transparent;
                        border: none;
                        padding: 0;
                    }}
                """)
                row_layout.addWidget(label)

                # 根据参数类型创建控件
                if param_type == "int":
                    widget = QSpinBox()
                    widget.setRange(param_config.get("min", -999999), param_config.get("max", 999999))
                    if default_value is not None:
                        widget.setValue(default_value)
                elif param_type == "float":
                    widget = QDoubleSpinBox()
                    widget.setRange(param_config.get("min", -999999.0), param_config.get("max", 999999.0))
                    widget.setDecimals(param_config.get("decimals", 2))
                    if default_value is not None:
                        widget.setValue(default_value)
                elif param_type == "bool":
                    widget = QCheckBox("启用")
                    if default_value is not None:
                        widget.setChecked(default_value)
                elif param_type == "choice":
                    widget = QComboBox()
                    choices = param_config.get("choices", [])
                    for choice in choices:
                        widget.addItem(str(choice))
                    if default_value is not None:
                        widget.setCurrentText(str(default_value))
                else:  # string
                    widget = QLineEdit()
                    if default_value is not None:
                        widget.setText(str(default_value))

                # 设置控件样式
                widget.setStyleSheet(f"""
                    QSpinBox, QDoubleSpinBox, QLineEdit, QComboBox {{
                        background-color: {THEME_COLORS["dark_bg_sidebar"]};
                        color: {THEME_COLORS["text_primary"]};
                        border: 1px solid {THEME_COLORS["dark_border_primary"]};
                        border-radius: 4px;
                        padding: 6px 8px;
                        font-size: 13px;
                    }}
                    QSpinBox:focus, QDoubleSpinBox:focus, QLineEdit:focus, QComboBox:focus {{
                        border-color: {THEME_COLORS["primary"]};
                    }}
                    QCheckBox {{
                        color: {THEME_COLORS["text_primary"]};
                        font-size: 13px;
                        background: transparent;
                        border: none;
                    }}
                    QCheckBox::indicator {{
                        width: 16px;
                        height: 16px;
                        border: 2px solid {THEME_COLORS["dark_border_primary"]};
                        border-radius: 3px;
                        background-color: {THEME_COLORS["dark_bg_sidebar"]};
                    }}
                    QCheckBox::indicator:checked {{
                        background-color: {THEME_COLORS["primary"]};
                        border-color: {THEME_COLORS["primary"]};
                    }}
                """)

                row_layout.addWidget(widget)
                container_layout.addWidget(param_row)

            # 添加到参数配置容器
            self.param_content_layout.addWidget(param_container)
            self.param_content_layout.addStretch()

        except Exception as e:
            logger.error(f"创建简单参数界面失败: {e}")

    def _get_default_parameter_schema(self, algorithm_name: str) -> Dict[str, Any]:
        """获取默认参数模式"""
        default_schemas = {
            "gaussian_blur": {
                "kernel_size": {"type": "int", "default": 5, "min": 1, "max": 31, "description": "核大小"},
                "sigma": {"type": "float", "default": 1.5, "min": 0.1, "max": 10.0, "description": "标准差"}
            },
            "canny_edge": {
                "threshold1": {"type": "int", "default": 100, "min": 0, "max": 255, "description": "低阈值"},
                "threshold2": {"type": "int", "default": 200, "min": 0, "max": 255, "description": "高阈值"}
            },
            "sobel_edge": {
                "ksize": {"type": "int", "default": 3, "min": 1, "max": 7, "description": "核大小"},
                "scale": {"type": "float", "default": 1.0, "min": 0.1, "max": 10.0, "description": "缩放因子"}
            },
            "contour_detection": {
                "mode": {"type": "choice", "default": "EXTERNAL", "choices": ["EXTERNAL", "LIST", "CCOMP", "TREE"], "description": "轮廓检索模式"},
                "method": {"type": "choice", "default": "SIMPLE", "choices": ["NONE", "SIMPLE", "TC89_L1", "TC89_KCOS"], "description": "轮廓近似方法"}
            },
            "template_matching": {
                "method": {"type": "choice", "default": "TM_CCOEFF_NORMED", "choices": ["TM_CCOEFF", "TM_CCOEFF_NORMED", "TM_CCORR", "TM_CCORR_NORMED"], "description": "匹配方法"},
                "threshold": {"type": "float", "default": 0.8, "min": 0.0, "max": 1.0, "description": "匹配阈值"}
            }
        }

        return default_schemas.get(algorithm_name, {
            "enabled": {"type": "bool", "default": True, "description": "启用算法"}
        })

    def _on_parameter_changed(self, param_name: str, value: Any):
        """参数改变事件"""
        if "parameters" not in self.config_data:
            self.config_data["parameters"] = {}

        self.config_data["parameters"][param_name] = value
        logger.debug(f"参数已更新: {param_name} = {value}")

    def _toggle_roi_drawing(self):
        """切换ROI绘制模式"""
        if self.roi_view.drawing_mode:
            self.roi_view.stop_roi_drawing()
            self.draw_roi_btn.setText("绘制ROI")
        else:
            self.roi_view.start_roi_drawing()
            self.draw_roi_btn.setText("停止绘制")

    def _clear_roi(self):
        """清除所有ROI"""
        self.roi_view.scene.clear()
        self.roi_view.roi_items.clear()
        self.roi_regions.clear()
        self.roi_list.clear()

        # 重新加载背景图像
        self._load_preview_image()

    def _on_roi_created(self, rect: QRectF):
        """ROI创建事件"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": int(rect.x()),
            "y": int(rect.y()),
            "width": int(rect.width()),
            "height": int(rect.height()),
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 选中新创建的ROI
        self.roi_list.setCurrentItem(item)

        logger.info(f"创建ROI: {roi_data}")

    def _on_roi_selection_changed(self):
        """ROI选择改变事件"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            roi_data = self.roi_regions[roi_index]

            # 更新ROI属性控件
            self.roi_name_edit.setText(roi_data["name"])
            self.roi_x_spin.setValue(roi_data["x"])
            self.roi_y_spin.setValue(roi_data["y"])
            self.roi_w_spin.setValue(roi_data["width"])
            self.roi_h_spin.setValue(roi_data["height"])

    def _add_roi(self):
        """添加ROI"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": 50,
            "y": 50,
            "width": 100,
            "height": 100,
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 在视图中添加ROI
        rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
        roi_rect = self.roi_view.scene.addRect(
            rect,
            QPen(QColor(THEME_COLORS["success"]), 2),
            QBrush(QColor(THEME_COLORS["success"]).lighter(150))
        )
        roi_rect.setOpacity(0.5)
        self.roi_view.roi_items.append(roi_rect)

        # 选中新添加的ROI
        self.roi_list.setCurrentItem(item)

    def _delete_roi(self):
        """删除选中的ROI"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            # 从数据中删除
            del self.roi_regions[roi_index]

            # 从列表中删除
            self.roi_list.takeItem(self.roi_list.row(current_item))

            # 重新绘制ROI
            self._redraw_roi()

    def _redraw_roi(self):
        """重新绘制所有ROI"""
        # 清除现有ROI
        for item in self.roi_view.roi_items:
            self.roi_view.scene.removeItem(item)
        self.roi_view.roi_items.clear()

        # 重新绘制
        for roi_data in self.roi_regions:
            rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
            roi_rect = self.roi_view.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["success"]), 2),
                QBrush(QColor(THEME_COLORS["success"]).lighter(150))
            )
            roi_rect.setOpacity(0.5)
            self.roi_view.roi_items.append(roi_rect)

    def _load_preview_image(self):
        """加载预览图像"""
        try:
            # 尝试从项目中加载一个示例图像
            import os
            import numpy as np
            import cv2
            from PyQt5.QtGui import QImage

            # 查找示例图像文件
            sample_image_paths = [
                "assets/sample_image.jpg",
                "assets/test_image.png",
                "docs/sample.jpg",
                "test_data/sample.png"
            ]

            loaded = False
            for image_path in sample_image_paths:
                if os.path.exists(image_path):
                    try:
                        # 使用OpenCV加载图像
                        image = cv2.imread(image_path)
                        if image is not None:
                            # 转换为RGB
                            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                            # 调整大小到合适的预览尺寸
                            height, width = image_rgb.shape[:2]
                            if width > 800 or height > 600:
                                scale = min(800/width, 600/height)
                                new_width = int(width * scale)
                                new_height = int(height * scale)
                                image_rgb = cv2.resize(image_rgb, (new_width, new_height))

                            # 转换为QPixmap
                            height, width, _ = image_rgb.shape
                            bytes_per_line = 3 * width
                            q_image = QImage(image_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
                            pixmap = QPixmap.fromImage(q_image)

                            self.roi_view.set_image(pixmap)
                            loaded = True
                            logger.info(f"加载示例图像: {image_path}")
                            break
                    except Exception as e:
                        logger.warning(f"加载图像 {image_path} 失败: {e}")
                        continue

            # 如果没有找到示例图像，创建一个测试图像
            if not loaded:
                self._create_test_image()

        except Exception as e:
            logger.error(f"加载预览图像失败: {e}")
            self._create_test_image()

    def _create_test_image(self):
        """创建测试图像"""
        try:
            import numpy as np
            import cv2
            from PyQt5.QtGui import QImage

            # 创建一个更有趣的测试图像
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)

            # 创建渐变背景
            for y in range(480):
                for x in range(640):
                    test_image[y, x] = [
                        int(64 + (x / 640) * 64),  # R
                        int(64 + (y / 480) * 64),  # G
                        int(128 - (x / 640) * 64)  # B
                    ]

            # 添加一些几何图形用于测试
            # 绘制矩形
            cv2.rectangle(test_image, (100, 100), (200, 200), (255, 255, 255), 2)
            cv2.rectangle(test_image, (120, 120), (180, 180), (0, 255, 0), -1)

            # 绘制圆形
            cv2.circle(test_image, (400, 150), 50, (255, 0, 0), 2)
            cv2.circle(test_image, (400, 150), 30, (0, 0, 255), -1)

            # 绘制线条
            cv2.line(test_image, (50, 300), (590, 300), (255, 255, 0), 3)
            cv2.line(test_image, (320, 50), (320, 430), (255, 0, 255), 3)

            # 添加文字
            cv2.putText(test_image, "WireVision Test Image", (150, 350),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(test_image, "Algorithm Preview", (200, 400),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (200, 200, 200), 2)

            # 转换为QPixmap - 修复BGR到RGB通道问题
            height, width, _ = test_image.shape
            # OpenCV使用BGR格式，需要转换为RGB
            rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
            bytes_per_line = 3 * width
            q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)

            self.roi_view.set_image(pixmap)
            logger.info("创建测试图像成功")

        except Exception as e:
            logger.error(f"创建测试图像失败: {e}")

    def _load_template(self):
        """加载参数模板"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)

                # 应用模板数据
                self.config_data.update(template_data)
                self._apply_config_to_ui()

                QMessageBox.information(self, "成功", "参数模板已加载")

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.warning(self, "错误", f"加载模板失败: {str(e)}")

    def _save_template(self):
        """保存参数模板"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", "参数模板已保存")

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            QMessageBox.warning(self, "错误", f"保存模板失败: {str(e)}")

    def _apply_config_to_ui(self):
        """将配置应用到UI"""
        try:
            # 应用算法选择
            algorithm_name = self.config_data.get("algorithm", "")
            if algorithm_name:
                for i in range(self.algorithm_combo.count()):
                    if self.algorithm_combo.itemData(i) == algorithm_name:
                        self.algorithm_combo.setCurrentIndex(i)
                        break

            # 应用ROI配置
            self.roi_regions = self.config_data.get("roi_regions", [])
            self._update_roi_list()
            self._redraw_roi()

            # 应用输入输出配置
            self.input_enabled.setChecked(self.config_data.get("input_enabled", True))
            self.output_enabled.setChecked(self.config_data.get("output_enabled", True))

        except Exception as e:
            logger.error(f"应用配置到UI失败: {e}")

    def _update_roi_list(self):
        """更新ROI列表"""
        self.roi_list.clear()

        for i, roi_data in enumerate(self.roi_regions):
            item = QListWidgetItem(roi_data["name"])
            item.setData(Qt.UserRole, i)
            self.roi_list.addItem(item)

    def _reset_config(self):
        """重置配置"""
        self.config_data = self._get_default_config()
        self._apply_config_to_ui()
        self._clear_roi()

    def _apply_config(self):
        """应用配置"""
        # 收集当前配置
        self._collect_current_config()

        # 发射配置改变信号
        self.config_changed.emit(self.config_data)

        QMessageBox.information(self, "成功", "配置已应用")

    def _collect_current_config(self):
        """收集当前配置"""
        # 收集算法配置
        self.config_data["algorithm"] = self.algorithm_combo.currentData() or ""

        # 收集参数配置
        if hasattr(self, 'current_algorithm_ui') and hasattr(self.current_algorithm_ui, 'get_parameters'):
            self.config_data["parameters"] = self.current_algorithm_ui.get_parameters()

        # 收集ROI配置
        self.config_data["roi_regions"] = self.roi_regions.copy()

        # 收集输入输出配置（默认启用）
        self.config_data["input_enabled"] = True
        self.config_data["output_enabled"] = True

    def accept(self):
        """确定按钮"""
        self._collect_current_config()
        self.config_changed.emit(self.config_data)
        super().accept()

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)

        # 加载预览图像
        QTimer.singleShot(100, self._load_preview_image)
