#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化节点配置对话框

提供完整的节点配置功能：
- 算法参数设置
- ROI区域配置和绘制
- 输入输出设置
- 显示选项配置
- 实时预览
- 参数模板
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QSlider, QGroupBox,
    QTextEdit, QScrollArea, QFrame, QSizePolicy, QMessageBox,
    QApplication, QSplitter, QListWidget, QListWidgetItem,
    QGraphicsView, QGraphicsScene, QGraphicsRectItem, QGraphicsEllipseItem,
    QGraphicsTextItem, QFileDialog, QProgressBar, QToolButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QRectF, QPointF, QThread, pyqtSlot
from PyQt5.QtGui import (
    QFont, QIcon, QPixmap, QPainter, QColor, QPen, QBrush,
    QCursor, QMouseEvent, QPainterPath
)
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger
import json
import os

from wirevsion.ui.modern_components import ModernCard, ModernButton, THEME_COLORS

# 尝试导入算法相关模块，如果失败则使用简化版本
try:
    from wirevsion.ui.algorithm_ui_manager import AlgorithmUIManager
except ImportError:
    AlgorithmUIManager = None

try:
    from wirevsion.algorithms.registry import AlgorithmRegistry
except ImportError:
    AlgorithmRegistry = None


class ROIDrawingView(QGraphicsView):
    """ROI绘制视图"""

    roi_created = pyqtSignal(QRectF)  # ROI创建信号
    roi_selected = pyqtSignal(int)    # ROI选择信号

    def __init__(self, parent=None):
        super().__init__(parent)

        self.scene = QGraphicsScene()
        self.setScene(self.scene)

        # 绘制状态
        self.drawing_mode = False
        self.start_point = None
        self.current_rect = None
        self.roi_items = []

        # 设置视图属性
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)

        # 样式
        self.setStyleSheet(f"""
            QGraphicsView {{
                background-color: {THEME_COLORS["dark_bg_card"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
            }}
        """)

    def set_image(self, pixmap: QPixmap):
        """设置背景图像"""
        self.scene.clear()
        self.roi_items.clear()

        if pixmap and not pixmap.isNull():
            self.scene.addPixmap(pixmap)
            self.scene.setSceneRect(pixmap.rect())
            self.fitInView(self.scene.itemsBoundingRect(), Qt.KeepAspectRatio)

    def start_roi_drawing(self):
        """开始ROI绘制模式"""
        self.drawing_mode = True
        self.setCursor(Qt.CrossCursor)
        self.setDragMode(QGraphicsView.NoDrag)

    def stop_roi_drawing(self):
        """停止ROI绘制模式"""
        self.drawing_mode = False
        self.setCursor(Qt.ArrowCursor)
        self.setDragMode(QGraphicsView.RubberBandDrag)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton:
            self.start_point = self.mapToScene(event.pos())
            self.current_rect = None
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing_mode and self.start_point:
            current_point = self.mapToScene(event.pos())

            # 移除之前的临时矩形
            if self.current_rect:
                self.scene.removeItem(self.current_rect)

            # 创建新的临时矩形
            rect = QRectF(self.start_point, current_point).normalized()
            self.current_rect = self.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["primary"]), 2),
                QBrush(QColor(THEME_COLORS["primary"]).lighter(150))
            )
            self.current_rect.setOpacity(0.3)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton and self.start_point:
            end_point = self.mapToScene(event.pos())
            rect = QRectF(self.start_point, end_point).normalized()

            # 检查矩形大小
            if rect.width() > 10 and rect.height() > 10:
                # 移除临时矩形
                if self.current_rect:
                    self.scene.removeItem(self.current_rect)

                # 创建正式的ROI
                roi_rect = self.scene.addRect(
                    rect,
                    QPen(QColor(THEME_COLORS["success"]), 2),
                    QBrush(QColor(THEME_COLORS["success"]).lighter(150))
                )
                roi_rect.setOpacity(0.5)
                self.roi_items.append(roi_rect)

                # 发射信号
                self.roi_created.emit(rect)

            self.start_point = None
            self.current_rect = None
        else:
            super().mouseReleaseEvent(event)


class ModernNodeConfigDialog(QDialog):
    """现代化节点配置对话框"""

    config_changed = pyqtSignal(dict)  # 配置改变信号

    def __init__(self, node, parent=None):
        super().__init__(parent)

        self.node = node
        self.node_id = node.node_id
        self.node_type = node.node_type
        self.node_title = node.title

        # 算法UI管理器
        self.algorithm_ui_manager = AlgorithmUIManager() if AlgorithmUIManager else None
        self.algorithm_registry = AlgorithmRegistry() if AlgorithmRegistry else None

        # 配置数据
        self.config_data = self._get_default_config()
        self.roi_regions = []

        self._setup_ui()
        self._load_algorithm_config()

        logger.info(f"创建现代化节点配置对话框: {self.node_id}")

    def _setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(f"配置节点 - {self.node_title}")
        self.setModal(True)
        self.resize(1000, 700)

        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {THEME_COLORS["dark_bg_app"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
            }}
        """)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 标题栏
        self._create_title_bar(layout)

        # 内容区域
        self._create_content_area(layout)

        # 按钮栏
        self._create_button_bar(layout)

    def _create_title_bar(self, layout):
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["primary"]};
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)

        # 标题
        title = QLabel(f"配置节点 - {self.node_title}")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary"]};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        title_layout.addWidget(title)

        # 节点类型标签
        type_label = QLabel(f"类型: {self.node_type}")
        type_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary"]};
                font-size: 12px;
                opacity: 0.8;
            }}
        """)
        title_layout.addWidget(type_label)

        title_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        """)
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)

        layout.addWidget(title_bar)

    def _create_content_area(self, layout):
        """创建内容区域"""
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：参数配置
        self._create_parameter_panel(splitter)

        # 右侧：预览和ROI
        self._create_preview_panel(splitter)

        # 设置分割比例
        splitter.setSizes([400, 600])

        content_layout.addWidget(splitter)
        layout.addWidget(content, 1)

    def _create_parameter_panel(self, splitter):
        """创建参数配置面板"""
        param_widget = QWidget()
        param_layout = QVBoxLayout(param_widget)
        param_layout.setContentsMargins(5, 5, 5, 5)

        # 选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                background-color: {THEME_COLORS["dark_bg_content"]};
            }}
            QTabBar::tab {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                color: {THEME_COLORS["text_primary"]};
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }}
            QTabBar::tab:selected {{
                background-color: {THEME_COLORS["primary"]};
                color: {THEME_COLORS["text_on_primary"]};
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {THEME_COLORS["dark_surface_hover"]};
            }}
        """)

        # 算法参数选项卡
        self.algorithm_tab = QWidget()
        self._setup_algorithm_tab()
        self.tab_widget.addTab(self.algorithm_tab, "算法参数")

        # ROI配置选项卡
        self.roi_tab = QWidget()
        self._setup_roi_tab()
        self.tab_widget.addTab(self.roi_tab, "ROI配置")

        # 输入输出选项卡
        self.io_tab = QWidget()
        self._setup_io_tab()
        self.tab_widget.addTab(self.io_tab, "输入输出")

        param_layout.addWidget(self.tab_widget)
        splitter.addWidget(param_widget)

    def _create_preview_panel(self, splitter):
        """创建预览面板"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(5, 5, 5, 5)

        # 预览标题
        preview_title = QLabel("实时预览")
        preview_title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_title"]};
                font-size: 14px;
                font-weight: bold;
                padding: 5px 0;
            }}
        """)
        preview_layout.addWidget(preview_title)

        # ROI绘制视图
        self.roi_view = ROIDrawingView()
        self.roi_view.roi_created.connect(self._on_roi_created)
        preview_layout.addWidget(self.roi_view, 1)

        # ROI控制按钮
        roi_controls = QWidget()
        roi_controls_layout = QHBoxLayout(roi_controls)
        roi_controls_layout.setContentsMargins(0, 5, 0, 0)

        self.draw_roi_btn = ModernButton("绘制ROI", ModernButton.PRIMARY)
        self.draw_roi_btn.clicked.connect(self._toggle_roi_drawing)
        roi_controls_layout.addWidget(self.draw_roi_btn)

        self.clear_roi_btn = ModernButton("清除ROI", ModernButton.SECONDARY)
        self.clear_roi_btn.clicked.connect(self._clear_roi)
        roi_controls_layout.addWidget(self.clear_roi_btn)

        roi_controls_layout.addStretch()

        preview_layout.addWidget(roi_controls)
        splitter.addWidget(preview_widget)

    def _create_button_bar(self, layout):
        """创建按钮栏"""
        button_bar = QWidget()
        button_bar.setFixedHeight(60)
        button_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        button_layout = QHBoxLayout(button_bar)
        button_layout.setContentsMargins(15, 0, 15, 0)

        # 模板按钮
        template_btn = ModernButton("加载模板", ModernButton.SECONDARY)
        template_btn.clicked.connect(self._load_template)
        button_layout.addWidget(template_btn)

        save_template_btn = ModernButton("保存模板", ModernButton.SECONDARY)
        save_template_btn.clicked.connect(self._save_template)
        button_layout.addWidget(save_template_btn)

        button_layout.addStretch()

        # 主要按钮
        reset_btn = ModernButton("重置", ModernButton.SECONDARY)
        reset_btn.clicked.connect(self._reset_config)
        button_layout.addWidget(reset_btn)

        apply_btn = ModernButton("应用", ModernButton.SUCCESS)
        apply_btn.clicked.connect(self._apply_config)
        button_layout.addWidget(apply_btn)

        ok_btn = ModernButton("确定", ModernButton.PRIMARY)
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = ModernButton("取消", ModernButton.SECONDARY)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addWidget(button_bar)

    def _setup_algorithm_tab(self):
        """设置算法参数选项卡"""
        layout = QVBoxLayout(self.algorithm_tab)
        layout.setContentsMargins(10, 10, 10, 10)

        # 算法选择
        algo_group = QGroupBox("算法选择")
        algo_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-weight: bold;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """)
        algo_layout = QVBoxLayout(algo_group)

        # 算法下拉框
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 25px;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {THEME_COLORS["text_secondary"]};
            }}
        """)
        self.algorithm_combo.currentTextChanged.connect(self._on_algorithm_changed)
        algo_layout.addWidget(self.algorithm_combo)

        layout.addWidget(algo_group)

        # 参数配置容器
        self.param_container = QScrollArea()
        self.param_container.setWidgetResizable(True)
        self.param_container.setStyleSheet(f"""
            QScrollArea {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
            }}
        """)

        self.param_widget = QWidget()
        self.param_layout = QVBoxLayout(self.param_widget)
        self.param_container.setWidget(self.param_widget)

        layout.addWidget(self.param_container, 1)

    def _setup_roi_tab(self):
        """设置ROI配置选项卡"""
        layout = QVBoxLayout(self.roi_tab)
        layout.setContentsMargins(10, 10, 10, 10)

        # ROI列表
        roi_group = QGroupBox("ROI区域列表")
        roi_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-weight: bold;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """)
        roi_layout = QVBoxLayout(roi_group)

        # ROI列表控件
        self.roi_list = QListWidget()
        self.roi_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
            QListWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
            QListWidget::item:selected {{
                background-color: {THEME_COLORS["primary"]};
                color: {THEME_COLORS["text_on_primary"]};
            }}
            QListWidget::item:hover {{
                background-color: {THEME_COLORS["dark_surface_hover"]};
            }}
        """)
        self.roi_list.itemSelectionChanged.connect(self._on_roi_selection_changed)
        roi_layout.addWidget(self.roi_list)

        # ROI操作按钮
        roi_btn_layout = QHBoxLayout()

        add_roi_btn = ModernButton("添加ROI", ModernButton.SUCCESS)
        add_roi_btn.clicked.connect(self._add_roi)
        roi_btn_layout.addWidget(add_roi_btn)

        delete_roi_btn = ModernButton("删除ROI", ModernButton.DANGER)
        delete_roi_btn.clicked.connect(self._delete_roi)
        roi_btn_layout.addWidget(delete_roi_btn)

        roi_btn_layout.addStretch()
        roi_layout.addLayout(roi_btn_layout)

        layout.addWidget(roi_group)

        # ROI属性配置
        prop_group = QGroupBox("ROI属性")
        prop_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-weight: bold;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """)
        prop_layout = QFormLayout(prop_group)

        # ROI名称
        self.roi_name_edit = QLineEdit()
        self.roi_name_edit.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        prop_layout.addRow("名称:", self.roi_name_edit)

        # ROI坐标
        coord_layout = QHBoxLayout()

        self.roi_x_spin = QSpinBox()
        self.roi_x_spin.setRange(0, 9999)
        self.roi_x_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        coord_layout.addWidget(QLabel("X:"))
        coord_layout.addWidget(self.roi_x_spin)

        self.roi_y_spin = QSpinBox()
        self.roi_y_spin.setRange(0, 9999)
        self.roi_y_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        coord_layout.addWidget(QLabel("Y:"))
        coord_layout.addWidget(self.roi_y_spin)

        prop_layout.addRow("位置:", coord_layout)

        # ROI尺寸
        size_layout = QHBoxLayout()

        self.roi_w_spin = QSpinBox()
        self.roi_w_spin.setRange(1, 9999)
        self.roi_w_spin.setValue(100)
        self.roi_w_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        size_layout.addWidget(QLabel("宽:"))
        size_layout.addWidget(self.roi_w_spin)

        self.roi_h_spin = QSpinBox()
        self.roi_h_spin.setRange(1, 9999)
        self.roi_h_spin.setValue(100)
        self.roi_h_spin.setStyleSheet(f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 5px;
            }}
        """)
        size_layout.addWidget(QLabel("高:"))
        size_layout.addWidget(self.roi_h_spin)

        prop_layout.addRow("尺寸:", size_layout)

        layout.addWidget(prop_group)
        layout.addStretch()

    def _setup_io_tab(self):
        """设置输入输出选项卡"""
        layout = QVBoxLayout(self.io_tab)
        layout.setContentsMargins(10, 10, 10, 10)

        # 输入配置
        input_group = QGroupBox("输入配置")
        input_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-weight: bold;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """)
        input_layout = QFormLayout(input_group)

        # 输入端口配置
        self.input_enabled = QCheckBox("启用输入")
        self.input_enabled.setChecked(True)
        self.input_enabled.setStyleSheet(f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 3px;
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                background-color: {THEME_COLORS["primary"]};
                border-color: {THEME_COLORS["primary"]};
            }}
        """)
        input_layout.addRow(self.input_enabled)

        layout.addWidget(input_group)

        # 输出配置
        output_group = QGroupBox("输出配置")
        output_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-weight: bold;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }}
        """)
        output_layout = QFormLayout(output_group)

        # 输出端口配置
        self.output_enabled = QCheckBox("启用输出")
        self.output_enabled.setChecked(True)
        self.output_enabled.setStyleSheet(f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 3px;
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                background-color: {THEME_COLORS["primary"]};
                border-color: {THEME_COLORS["primary"]};
            }}
        """)
        output_layout.addRow(self.output_enabled)

        layout.addWidget(output_group)
        layout.addStretch()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "algorithm": "",
            "parameters": {},
            "roi_regions": [],
            "input_enabled": True,
            "output_enabled": True,
            "display_options": {
                "show_roi": True,
                "show_results": True,
                "overlay_color": "#00FF00"
            }
        }

    def _load_algorithm_config(self):
        """加载算法配置"""
        try:
            # 填充算法下拉框
            self.algorithm_combo.clear()
            self.algorithm_combo.addItem("选择算法...", "")

            if self.algorithm_registry:
                # 获取所有算法
                all_algorithms = self.algorithm_registry.get_all_algorithms_flat()

                for algorithm_name, algorithm_class in all_algorithms.items():
                    try:
                        algorithm = algorithm_class()
                        display_name = algorithm.get_display_name()
                        self.algorithm_combo.addItem(display_name, algorithm_name)
                    except Exception as e:
                        logger.warning(f"加载算法失败 {algorithm_name}: {e}")

                logger.info(f"已加载 {len(all_algorithms)} 个算法到配置对话框")
            else:
                # 添加一些默认算法选项
                default_algorithms = [
                    ("高斯模糊", "gaussian_blur"),
                    ("Canny边缘检测", "canny_edge"),
                    ("Sobel边缘检测", "sobel_edge"),
                    ("轮廓检测", "contour_detection"),
                    ("模板匹配", "template_matching"),
                ]

                for display_name, algorithm_name in default_algorithms:
                    self.algorithm_combo.addItem(display_name, algorithm_name)

                logger.info(f"已加载 {len(default_algorithms)} 个默认算法到配置对话框")

        except Exception as e:
            logger.error(f"加载算法配置失败: {e}")

    def _on_algorithm_changed(self, algorithm_display_name: str):
        """算法选择改变事件"""
        algorithm_name = self.algorithm_combo.currentData()

        if not algorithm_name:
            return

        try:
            # 清除之前的参数界面
            for i in reversed(range(self.param_layout.count())):
                child = self.param_layout.itemAt(i).widget()
                if child:
                    child.deleteLater()

            # 创建新的算法UI
            if self.algorithm_ui_manager:
                algorithm_ui = self.algorithm_ui_manager.create_algorithm_ui(algorithm_name)

                if algorithm_ui:
                    self.param_layout.addWidget(algorithm_ui)
                    self.current_algorithm_ui = algorithm_ui

                    # 连接参数改变信号
                    if hasattr(algorithm_ui, 'parameter_changed'):
                        algorithm_ui.parameter_changed.connect(self._on_parameter_changed)

                    logger.info(f"已加载算法UI: {algorithm_name}")
                else:
                    # 创建简单的参数界面
                    self._create_simple_parameter_ui(algorithm_name)
            else:
                # 创建简单的参数界面
                self._create_simple_parameter_ui(algorithm_name)

        except Exception as e:
            logger.error(f"切换算法失败: {e}")
            QMessageBox.warning(self, "错误", f"切换算法失败: {str(e)}")

    def _create_simple_parameter_ui(self, algorithm_name: str):
        """创建简单的参数界面"""
        try:
            # 获取参数模式
            param_schema = {}

            if self.algorithm_registry:
                # 获取算法实例
                algorithm_class = self.algorithm_registry.get_algorithm(algorithm_name)
                if algorithm_class:
                    algorithm = algorithm_class()
                    param_schema = algorithm.get_parameter_schema()

            # 如果没有参数模式，使用默认参数
            if not param_schema:
                param_schema = self._get_default_parameter_schema(algorithm_name)

            # 创建参数控件
            for param_name, param_config in param_schema.items():
                param_type = param_config.get("type", "string")
                description = param_config.get("description", param_name)
                default_value = param_config.get("default")

                # 创建参数组
                param_group = QGroupBox(description)
                param_group.setStyleSheet(f"""
                    QGroupBox {{
                        color: {THEME_COLORS["text_primary"]};
                        font-weight: bold;
                        border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                        border-radius: 4px;
                        margin-top: 8px;
                        padding-top: 8px;
                    }}
                    QGroupBox::title {{
                        subcontrol-origin: margin;
                        left: 8px;
                        padding: 0 4px;
                    }}
                """)
                param_layout = QHBoxLayout(param_group)

                # 根据参数类型创建控件
                if param_type == "int":
                    widget = QSpinBox()
                    widget.setRange(param_config.get("min", -999999), param_config.get("max", 999999))
                    if default_value is not None:
                        widget.setValue(default_value)
                elif param_type == "float":
                    widget = QDoubleSpinBox()
                    widget.setRange(param_config.get("min", -999999.0), param_config.get("max", 999999.0))
                    widget.setDecimals(param_config.get("decimals", 2))
                    if default_value is not None:
                        widget.setValue(default_value)
                elif param_type == "bool":
                    widget = QCheckBox()
                    if default_value is not None:
                        widget.setChecked(default_value)
                elif param_type == "choice":
                    widget = QComboBox()
                    choices = param_config.get("choices", [])
                    for choice in choices:
                        widget.addItem(str(choice))
                    if default_value is not None:
                        widget.setCurrentText(str(default_value))
                else:  # string
                    widget = QLineEdit()
                    if default_value is not None:
                        widget.setText(str(default_value))

                # 设置样式
                widget.setStyleSheet(f"""
                    QSpinBox, QDoubleSpinBox, QLineEdit, QComboBox {{
                        background-color: {THEME_COLORS["dark_bg_input"]};
                        color: {THEME_COLORS["text_primary"]};
                        border: 1px solid {THEME_COLORS["dark_border_primary"]};
                        border-radius: 4px;
                        padding: 5px;
                    }}
                    QCheckBox {{
                        color: {THEME_COLORS["text_primary"]};
                    }}
                """)

                param_layout.addWidget(widget)
                self.param_layout.addWidget(param_group)

        except Exception as e:
            logger.error(f"创建简单参数界面失败: {e}")

    def _get_default_parameter_schema(self, algorithm_name: str) -> Dict[str, Any]:
        """获取默认参数模式"""
        default_schemas = {
            "gaussian_blur": {
                "kernel_size": {"type": "int", "default": 5, "min": 1, "max": 31, "description": "核大小"},
                "sigma": {"type": "float", "default": 1.5, "min": 0.1, "max": 10.0, "description": "标准差"}
            },
            "canny_edge": {
                "threshold1": {"type": "int", "default": 100, "min": 0, "max": 255, "description": "低阈值"},
                "threshold2": {"type": "int", "default": 200, "min": 0, "max": 255, "description": "高阈值"}
            },
            "sobel_edge": {
                "ksize": {"type": "int", "default": 3, "min": 1, "max": 7, "description": "核大小"},
                "scale": {"type": "float", "default": 1.0, "min": 0.1, "max": 10.0, "description": "缩放因子"}
            },
            "contour_detection": {
                "mode": {"type": "choice", "default": "EXTERNAL", "choices": ["EXTERNAL", "LIST", "CCOMP", "TREE"], "description": "轮廓检索模式"},
                "method": {"type": "choice", "default": "SIMPLE", "choices": ["NONE", "SIMPLE", "TC89_L1", "TC89_KCOS"], "description": "轮廓近似方法"}
            },
            "template_matching": {
                "method": {"type": "choice", "default": "TM_CCOEFF_NORMED", "choices": ["TM_CCOEFF", "TM_CCOEFF_NORMED", "TM_CCORR", "TM_CCORR_NORMED"], "description": "匹配方法"},
                "threshold": {"type": "float", "default": 0.8, "min": 0.0, "max": 1.0, "description": "匹配阈值"}
            }
        }

        return default_schemas.get(algorithm_name, {
            "enabled": {"type": "bool", "default": True, "description": "启用算法"}
        })

    def _on_parameter_changed(self, param_name: str, value: Any):
        """参数改变事件"""
        if "parameters" not in self.config_data:
            self.config_data["parameters"] = {}

        self.config_data["parameters"][param_name] = value
        logger.debug(f"参数已更新: {param_name} = {value}")

    def _toggle_roi_drawing(self):
        """切换ROI绘制模式"""
        if self.roi_view.drawing_mode:
            self.roi_view.stop_roi_drawing()
            self.draw_roi_btn.setText("绘制ROI")
        else:
            self.roi_view.start_roi_drawing()
            self.draw_roi_btn.setText("停止绘制")

    def _clear_roi(self):
        """清除所有ROI"""
        self.roi_view.scene.clear()
        self.roi_view.roi_items.clear()
        self.roi_regions.clear()
        self.roi_list.clear()

        # 重新加载背景图像
        self._load_preview_image()

    def _on_roi_created(self, rect: QRectF):
        """ROI创建事件"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": int(rect.x()),
            "y": int(rect.y()),
            "width": int(rect.width()),
            "height": int(rect.height()),
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 选中新创建的ROI
        self.roi_list.setCurrentItem(item)

        logger.info(f"创建ROI: {roi_data}")

    def _on_roi_selection_changed(self):
        """ROI选择改变事件"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            roi_data = self.roi_regions[roi_index]

            # 更新ROI属性控件
            self.roi_name_edit.setText(roi_data["name"])
            self.roi_x_spin.setValue(roi_data["x"])
            self.roi_y_spin.setValue(roi_data["y"])
            self.roi_w_spin.setValue(roi_data["width"])
            self.roi_h_spin.setValue(roi_data["height"])

    def _add_roi(self):
        """添加ROI"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": 50,
            "y": 50,
            "width": 100,
            "height": 100,
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 在视图中添加ROI
        rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
        roi_rect = self.roi_view.scene.addRect(
            rect,
            QPen(QColor(THEME_COLORS["success"]), 2),
            QBrush(QColor(THEME_COLORS["success"]).lighter(150))
        )
        roi_rect.setOpacity(0.5)
        self.roi_view.roi_items.append(roi_rect)

        # 选中新添加的ROI
        self.roi_list.setCurrentItem(item)

    def _delete_roi(self):
        """删除选中的ROI"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            # 从数据中删除
            del self.roi_regions[roi_index]

            # 从列表中删除
            self.roi_list.takeItem(self.roi_list.row(current_item))

            # 重新绘制ROI
            self._redraw_roi()

    def _redraw_roi(self):
        """重新绘制所有ROI"""
        # 清除现有ROI
        for item in self.roi_view.roi_items:
            self.roi_view.scene.removeItem(item)
        self.roi_view.roi_items.clear()

        # 重新绘制
        for roi_data in self.roi_regions:
            rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
            roi_rect = self.roi_view.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["success"]), 2),
                QBrush(QColor(THEME_COLORS["success"]).lighter(150))
            )
            roi_rect.setOpacity(0.5)
            self.roi_view.roi_items.append(roi_rect)

    def _load_preview_image(self):
        """加载预览图像"""
        try:
            # 创建示例图像
            import numpy as np
            import cv2
            from PyQt5.QtGui import QImage

            # 创建示例图像
            image = np.zeros((480, 640, 3), dtype=np.uint8)
            image.fill(50)  # 深灰色背景

            # 添加一些图案
            cv2.rectangle(image, (100, 100), (300, 200), (100, 150, 200), -1)
            cv2.circle(image, (400, 300), 80, (200, 100, 100), -1)
            cv2.putText(image, "Preview Image", (200, 350), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            # 转换为QPixmap
            height, width, channel = image.shape
            bytes_per_line = 3 * width
            q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_image)

            # 设置到视图
            self.roi_view.set_image(pixmap)

        except Exception as e:
            logger.error(f"加载预览图像失败: {e}")

    def _load_template(self):
        """加载参数模板"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)

                # 应用模板数据
                self.config_data.update(template_data)
                self._apply_config_to_ui()

                QMessageBox.information(self, "成功", "参数模板已加载")

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.warning(self, "错误", f"加载模板失败: {str(e)}")

    def _save_template(self):
        """保存参数模板"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", "参数模板已保存")

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            QMessageBox.warning(self, "错误", f"保存模板失败: {str(e)}")

    def _apply_config_to_ui(self):
        """将配置应用到UI"""
        try:
            # 应用算法选择
            algorithm_name = self.config_data.get("algorithm", "")
            if algorithm_name:
                for i in range(self.algorithm_combo.count()):
                    if self.algorithm_combo.itemData(i) == algorithm_name:
                        self.algorithm_combo.setCurrentIndex(i)
                        break

            # 应用ROI配置
            self.roi_regions = self.config_data.get("roi_regions", [])
            self._update_roi_list()
            self._redraw_roi()

            # 应用输入输出配置
            self.input_enabled.setChecked(self.config_data.get("input_enabled", True))
            self.output_enabled.setChecked(self.config_data.get("output_enabled", True))

        except Exception as e:
            logger.error(f"应用配置到UI失败: {e}")

    def _update_roi_list(self):
        """更新ROI列表"""
        self.roi_list.clear()

        for i, roi_data in enumerate(self.roi_regions):
            item = QListWidgetItem(roi_data["name"])
            item.setData(Qt.UserRole, i)
            self.roi_list.addItem(item)

    def _reset_config(self):
        """重置配置"""
        self.config_data = self._get_default_config()
        self._apply_config_to_ui()
        self._clear_roi()

    def _apply_config(self):
        """应用配置"""
        # 收集当前配置
        self._collect_current_config()

        # 发射配置改变信号
        self.config_changed.emit(self.config_data)

        QMessageBox.information(self, "成功", "配置已应用")

    def _collect_current_config(self):
        """收集当前配置"""
        # 收集算法配置
        self.config_data["algorithm"] = self.algorithm_combo.currentData() or ""

        # 收集参数配置
        if hasattr(self, 'current_algorithm_ui') and hasattr(self.current_algorithm_ui, 'get_parameters'):
            self.config_data["parameters"] = self.current_algorithm_ui.get_parameters()

        # 收集ROI配置
        self.config_data["roi_regions"] = self.roi_regions.copy()

        # 收集输入输出配置
        self.config_data["input_enabled"] = self.input_enabled.isChecked()
        self.config_data["output_enabled"] = self.output_enabled.isChecked()

    def accept(self):
        """确定按钮"""
        self._collect_current_config()
        self.config_changed.emit(self.config_data)
        super().accept()

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)

        # 加载预览图像
        QTimer.singleShot(100, self._load_preview_image)
