#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化节点配置对话框

提供完整的节点配置功能：
- 算法参数设置
- ROI区域配置和绘制
- 输入输出设置
- 显示选项配置
- 实时预览
- 参数模板
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QPushButton, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QCheckBox, QComboBox, QSlider, QGroupBox,
    QTextEdit, QScrollArea, QFrame, QSizePolicy, QMessageBox,
    QApplication, QSplitter, QListWidget, QListWidgetItem,
    QGraphicsView, QGraphicsScene, QGraphicsRectItem, QGraphicsEllipseItem,
    QGraphicsTextItem, QFileDialog, QProgressBar, QToolButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QRectF, QPointF, QThread, pyqtSlot
from PyQt5.QtGui import (
    QFont, QIcon, QPixmap, QPainter, QColor, QPen, QBrush,
    QCursor, QMouseEvent, QPainterPath
)
from typing import Dict, Any, Optional, List, Tuple
from loguru import logger
import json
import os

from wirevsion.ui.modern_components import ModernCard, ModernButton, THEME_COLORS
from wirevsion.ui.theme_manager import theme_manager

# 尝试导入算法相关模块，如果失败则使用简化版本
try:
    from wirevsion.ui.algorithm_ui_manager import AlgorithmUIManager
except ImportError:
    AlgorithmUIManager = None

try:
    from wirevsion.algorithms.registry import AlgorithmRegistry
except ImportError:
    AlgorithmRegistry = None

try:
    from wirevsion.ui.algorithm_config_widgets import AlgorithmConfigWidgetFactory
except ImportError:
    AlgorithmConfigWidgetFactory = None


class ROIDrawingView(QGraphicsView):
    """ROI绘制视图"""

    roi_created = pyqtSignal(QRectF)  # ROI创建信号
    roi_selected = pyqtSignal(int)    # ROI选择信号

    def __init__(self, parent=None):
        super().__init__(parent)

        # 创建场景
        self.scene = QGraphicsScene()
        self.setScene(self.scene)

        # 绘制状态
        self.drawing_mode = False
        self.start_point = None
        self.current_rect = None
        self.roi_items = []
        self.current_pixmap_item = None

        # 设置视图属性 - 关键渲染设置
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 确保视图可以接收更新
        self.setUpdatesEnabled(True)
        self.viewport().setUpdatesEnabled(True)

        # 设置缓存模式以提高性能
        self.setCacheMode(QGraphicsView.CacheBackground)

        # 设置变换锚点
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)

        # 样式 - 使用对比度更高的背景色便于调试
        self.setStyleSheet(f"""
            QGraphicsView {{
                background-color: #2a2a2a;
                border: 2px solid {THEME_COLORS["primary"]};
                border-radius: 4px;
            }}
        """)

        # 设置背景画刷为灰色，便于看到图像
        from PyQt5.QtGui import QBrush, QColor
        self.setBackgroundBrush(QBrush(QColor(42, 42, 42)))

    def set_image(self, pixmap: QPixmap):
        """设置背景图像 - 增强版本修复显示问题"""
        try:
            logger.debug(f"开始设置图像: pixmap={pixmap}, isNull={pixmap.isNull() if pixmap else 'None'}")

            # 清除现有内容
            self.scene.clear()
            self.roi_items.clear()
            self.current_pixmap_item = None

            # 保存当前图像
            self.current_pixmap = pixmap

            if pixmap and not pixmap.isNull():
                logger.debug(f"图像有效，尺寸: {pixmap.width()}x{pixmap.height()}")

                # 验证pixmap数据
                if pixmap.width() <= 0 or pixmap.height() <= 0:
                    logger.error(f"无效的图像尺寸: {pixmap.width()}x{pixmap.height()}")
                    return

                # 添加图像到场景并保存引用
                self.current_pixmap_item = self.scene.addPixmap(pixmap)

                # 验证图像项是否成功创建
                if not self.current_pixmap_item:
                    logger.error("无法创建图像项")
                    return

                # 确保图像项可见
                self.current_pixmap_item.setVisible(True)
                self.current_pixmap_item.setZValue(0)  # 设置为背景层

                # 设置场景矩形
                scene_rect = QRectF(pixmap.rect())
                self.scene.setSceneRect(scene_rect)

                logger.debug(f"场景矩形设置为: {scene_rect}")

                # 强制刷新场景
                self.scene.update()

                # 使用QTimer延迟调整视图，确保场景完全更新
                QTimer.singleShot(10, lambda: self._fit_view_to_image(scene_rect))

                # 额外的强制刷新
                QTimer.singleShot(50, self._force_view_refresh)

                # 强制重绘视图
                self.viewport().update()
                self.update()

                logger.debug(f"ROI视图设置图像成功: {pixmap.width()}x{pixmap.height()}")

                # 验证场景内容
                items = self.scene.items()
                logger.debug(f"场景中的项目数量: {len(items)}")

            else:
                logger.warning("ROI视图收到无效图像")
                # 设置一个默认的场景矩形
                self.scene.setSceneRect(0, 0, 640, 480)

        except Exception as e:
            logger.error(f"设置图像时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def _fit_view_to_image(self, scene_rect):
        """延迟调整视图以适应图像"""
        try:
            # 重置变换
            self.resetTransform()

            # 确保场景矩形有效
            if scene_rect.width() > 0 and scene_rect.height() > 0:
                # 适应视图
                self.fitInView(scene_rect, Qt.KeepAspectRatio)

                # 确保缩放不会太小
                current_transform = self.transform()
                scale_x = current_transform.m11()
                scale_y = current_transform.m22()

                # 如果缩放太小，设置最小缩放
                min_scale = 0.1
                if scale_x < min_scale or scale_y < min_scale:
                    self.resetTransform()
                    self.scale(min_scale, min_scale)
                    logger.debug(f"应用最小缩放: {min_scale}")

                # 强制重绘整个视图
                self.invalidateScene()
                self.viewport().repaint()
                self.repaint()

                logger.debug(f"视图已调整到场景矩形: {scene_rect}, 缩放: {scale_x:.3f}x{scale_y:.3f}")
            else:
                logger.warning(f"无效的场景矩形: {scene_rect}")

        except Exception as e:
            logger.error(f"调整视图时发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def _force_view_refresh(self):
        """强制刷新视图"""
        try:
            # 多种方式强制刷新
            self.scene.invalidate()
            self.invalidateScene()
            self.viewport().update()
            self.update()
            self.repaint()

            # 检查当前状态
            if self.current_pixmap_item:
                logger.debug(f"强制刷新后 - 图像项可见: {self.current_pixmap_item.isVisible()}")
                logger.debug(f"强制刷新后 - 场景项目数: {len(self.scene.items())}")

        except Exception as e:
            logger.error(f"强制刷新视图失败: {e}")

    def get_current_image(self):
        """获取当前图像"""
        return getattr(self, 'current_pixmap', None)

    def start_roi_drawing(self):
        """开始ROI绘制模式"""
        self.drawing_mode = True
        self.setCursor(Qt.CrossCursor)
        self.setDragMode(QGraphicsView.NoDrag)

    def stop_roi_drawing(self):
        """停止ROI绘制模式"""
        self.drawing_mode = False
        self.setCursor(Qt.ArrowCursor)
        self.setDragMode(QGraphicsView.RubberBandDrag)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton:
            self.start_point = self.mapToScene(event.pos())
            self.current_rect = None
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing_mode and self.start_point:
            current_point = self.mapToScene(event.pos())

            # 移除之前的临时矩形
            if self.current_rect:
                self.scene.removeItem(self.current_rect)

            # 创建新的临时矩形
            rect = QRectF(self.start_point, current_point).normalized()
            self.current_rect = self.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["primary"]), 2),
                QBrush(QColor(THEME_COLORS["primary"]).lighter(150))
            )
            self.current_rect.setOpacity(0.3)
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self.drawing_mode and event.button() == Qt.LeftButton and self.start_point:
            end_point = self.mapToScene(event.pos())
            rect = QRectF(self.start_point, end_point).normalized()

            # 检查矩形大小
            if rect.width() > 10 and rect.height() > 10:
                # 移除临时矩形
                if self.current_rect:
                    self.scene.removeItem(self.current_rect)

                # 创建正式的ROI
                roi_rect = self.scene.addRect(
                    rect,
                    QPen(QColor(THEME_COLORS["success"]), 2),
                    QBrush(QColor(THEME_COLORS["success"]).lighter(150))
                )
                roi_rect.setOpacity(0.5)
                self.roi_items.append(roi_rect)

                # 发射信号
                self.roi_created.emit(rect)

            self.start_point = None
            self.current_rect = None
        else:
            super().mouseReleaseEvent(event)


class ModernNodeConfigDialog(QDialog):
    """现代化节点配置对话框"""

    config_changed = pyqtSignal(dict)  # 配置改变信号

    def __init__(self, node, parent=None):
        super().__init__(parent)

        self.node = node
        self.node_id = node.node_id
        self.node_type = node.node_type
        self.node_title = node.title

        # 算法UI管理器
        self.algorithm_ui_manager = AlgorithmUIManager() if AlgorithmUIManager else None
        self.algorithm_registry = AlgorithmRegistry() if AlgorithmRegistry else None

        # 配置数据
        self.config_data = self._get_default_config()
        self.roi_regions = []

        self._setup_ui()
        self._load_algorithm_config()

        logger.info(f"创建现代化节点配置对话框: {self.node_id}")

    def _setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle(f"配置节点 - {self.node_title}")
        self.setModal(True)

        # 获取屏幕尺寸并设置响应式大小
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # 计算对话框尺寸（屏幕的80%，但不小于最小尺寸）
        min_width, min_height = 900, 650
        dialog_width = max(int(screen_width * 0.8), min_width)
        dialog_height = max(int(screen_height * 0.8), min_height)

        self.resize(dialog_width, dialog_height)

        # 居中显示
        self.move(
            (screen_width - dialog_width) // 2,
            (screen_height - dialog_height) // 2
        )

        # 设置样式
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {THEME_COLORS["dark_bg_app"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
            }}
        """)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 标题栏
        self._create_title_bar(layout)

        # 内容区域
        self._create_content_area(layout)

        # 按钮栏
        self._create_button_bar(layout)

    def _create_title_bar(self, layout):
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["primary"]};
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)

        # 标题
        title = QLabel(f"配置节点 - {self.node_title}")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary_bg"]};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        title_layout.addWidget(title)

        # 节点类型标签
        type_label = QLabel(f"类型: {self.node_type}")
        type_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_on_primary_bg"]};
                font-size: 12px;
                opacity: 0.8;
            }}
        """)
        title_layout.addWidget(type_label)

        title_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {THEME_COLORS["text_on_primary_bg"]};
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 15px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS["danger"]};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS["danger_pressed"]};
            }}
        """)
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)

        layout.addWidget(title_bar)

    def _create_content_area(self, layout):
        """创建内容区域"""
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 创建响应式分割器
        self.main_splitter = QSplitter(Qt.Horizontal)
        self.main_splitter.setHandleWidth(2)
        self.main_splitter.setStyleSheet(f"""
            QSplitter::handle {{
                background-color: {THEME_COLORS["dark_border_primary"]};
            }}
            QSplitter::handle:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """)

        # 左侧：参数配置面板
        self._create_parameter_panel(self.main_splitter)

        # 右侧：预览面板
        self._create_preview_panel(self.main_splitter)

        # 设置初始分割比例 (35% : 65%)
        total_width = self.width()
        left_width = int(total_width * 0.35)
        right_width = total_width - left_width
        self.main_splitter.setSizes([left_width, right_width])

        # 设置最小尺寸
        self.main_splitter.setChildrenCollapsible(False)

        content_layout.addWidget(self.main_splitter)
        layout.addWidget(content, 1)

        # 连接窗口大小变化事件
        self.original_resize_event = self.resizeEvent
        self.resizeEvent = self._on_resize_event

    def _on_resize_event(self, event):
        """窗口大小变化事件处理"""
        # 调用原始的resize事件
        if self.original_resize_event:
            self.original_resize_event(event)

        # 重新计算分割器比例
        if hasattr(self, 'main_splitter'):
            total_width = self.width()
            left_width = int(total_width * 0.35)
            right_width = total_width - left_width
            self.main_splitter.setSizes([left_width, right_width])

    def _create_parameter_panel(self, splitter):
        """创建参数配置面板"""
        # 创建主参数容器
        self.param_main_widget = QWidget()
        self.param_main_widget.setMinimumWidth(350)
        self.param_main_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border-right: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        param_layout = QVBoxLayout(self.param_main_widget)
        param_layout.setContentsMargins(0, 0, 0, 0)
        param_layout.setSpacing(0)

        # 创建算法配置区域（直接渲染，无选项卡）
        self._create_algorithm_config_area(param_layout)

        # 创建ROI配置区域
        self._create_roi_config_area(param_layout)

        splitter.addWidget(self.param_main_widget)

    def _create_algorithm_config_area(self, layout):
        """创建算法配置区域"""
        # 算法选择区域
        algo_select_widget = QWidget()
        algo_select_widget.setFixedHeight(80)
        algo_select_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border-bottom: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
        """)

        select_layout = QVBoxLayout(algo_select_widget)
        select_layout.setContentsMargins(20, 15, 20, 15)
        select_layout.setSpacing(8)

        # 算法选择标签
        select_label = QLabel("🔧 算法类型")
        select_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        select_layout.addWidget(select_label)

        # 算法选择下拉框
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.setStyleSheet(theme_manager.get_combobox_style())
        self.algorithm_combo.currentTextChanged.connect(self._on_algorithm_changed)
        select_layout.addWidget(self.algorithm_combo)

        layout.addWidget(algo_select_widget)

        # 参数配置容器（滚动区域）
        self.param_scroll_area = QScrollArea()
        self.param_scroll_area.setWidgetResizable(True)
        self.param_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.param_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.param_scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                width: 6px;
                border-radius: 3px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {THEME_COLORS["dark_border_primary"]};
                border-radius: 3px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """)

        # 参数配置内容容器
        self.param_content_widget = QWidget()
        self.param_content_layout = QVBoxLayout(self.param_content_widget)
        self.param_content_layout.setContentsMargins(10, 10, 10, 10)
        self.param_content_layout.setSpacing(8)

        self.param_scroll_area.setWidget(self.param_content_widget)
        layout.addWidget(self.param_scroll_area, 1)

    def _create_roi_config_area(self, layout):
        """创建ROI配置区域"""
        # ROI配置区域
        roi_widget = QWidget()
        roi_widget.setFixedHeight(140)
        roi_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                border-top: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
        """)

        roi_layout = QVBoxLayout(roi_widget)
        roi_layout.setContentsMargins(20, 15, 20, 15)
        roi_layout.setSpacing(8)

        # ROI标题
        roi_title = QLabel("📐 ROI区域")
        roi_title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        roi_layout.addWidget(roi_title)

        # ROI列表和控制区域
        roi_control_layout = QHBoxLayout()

        # ROI列表
        self.roi_list = QListWidget()
        self.roi_list.setMaximumHeight(60)
        self.roi_list.setStyleSheet(theme_manager.get_list_style())
        self.roi_list.itemSelectionChanged.connect(self._on_roi_selection_changed)
        roi_control_layout.addWidget(self.roi_list, 2)

        # ROI坐标控制
        roi_coords_layout = QVBoxLayout()

        # 第一行：X, Y
        coords_row1 = QHBoxLayout()
        self.roi_x_spin = QSpinBox()
        self.roi_x_spin.setRange(0, 9999)
        self.roi_x_spin.setPrefix("X:")
        self.roi_x_spin.setMaximumWidth(55)

        self.roi_y_spin = QSpinBox()
        self.roi_y_spin.setRange(0, 9999)
        self.roi_y_spin.setPrefix("Y:")
        self.roi_y_spin.setMaximumWidth(55)

        coords_row1.addWidget(self.roi_x_spin)
        coords_row1.addWidget(self.roi_y_spin)
        roi_coords_layout.addLayout(coords_row1)

        # 第二行：W, H
        coords_row2 = QHBoxLayout()
        self.roi_w_spin = QSpinBox()
        self.roi_w_spin.setRange(1, 9999)
        self.roi_w_spin.setValue(100)
        self.roi_w_spin.setPrefix("W:")
        self.roi_w_spin.setMaximumWidth(55)

        self.roi_h_spin = QSpinBox()
        self.roi_h_spin.setRange(1, 9999)
        self.roi_h_spin.setValue(100)
        self.roi_h_spin.setPrefix("H:")
        self.roi_h_spin.setMaximumWidth(55)

        coords_row2.addWidget(self.roi_w_spin)
        coords_row2.addWidget(self.roi_h_spin)
        roi_coords_layout.addLayout(coords_row2)

        # 设置坐标控件样式
        for spin in [self.roi_x_spin, self.roi_y_spin, self.roi_w_spin, self.roi_h_spin]:
            spin.setStyleSheet(theme_manager.get_spinbox_style())

        roi_control_layout.addLayout(roi_coords_layout, 1)
        roi_layout.addLayout(roi_control_layout)

        # ROI名称输入
        self.roi_name_edit = QLineEdit()
        self.roi_name_edit.setPlaceholderText("ROI名称")
        self.roi_name_edit.setMaximumHeight(25)
        self.roi_name_edit.setStyleSheet(f"""
            QLineEdit {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                padding: 4px 6px;
                font-size: 12px;
            }}
        """)
        roi_layout.addWidget(self.roi_name_edit)

        layout.addWidget(roi_widget)

    def _create_preview_panel(self, splitter):
        """创建预览面板"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(5, 5, 5, 5)

        # 预览标题
        preview_title = QLabel("实时预览")
        preview_title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_title"]};
                font-size: 14px;
                font-weight: bold;
                padding: 5px 0;
            }}
        """)
        preview_layout.addWidget(preview_title)

        # ROI绘制视图
        self.roi_view = ROIDrawingView()
        self.roi_view.roi_created.connect(self._on_roi_created)
        preview_layout.addWidget(self.roi_view, 1)

        # 立即加载预览图像 - 增加延迟确保UI完全初始化
        QTimer.singleShot(200, self._load_preview_image)

        # 添加调试信息
        QTimer.singleShot(500, self._debug_roi_view)

        # ROI控制按钮
        roi_controls = QWidget()
        roi_controls_layout = QHBoxLayout(roi_controls)
        roi_controls_layout.setContentsMargins(0, 5, 0, 0)

        self.draw_roi_btn = ModernButton("绘制ROI", ModernButton.PRIMARY)
        self.draw_roi_btn.clicked.connect(self._toggle_roi_drawing)
        roi_controls_layout.addWidget(self.draw_roi_btn)

        self.clear_roi_btn = ModernButton("清除ROI", ModernButton.SECONDARY)
        self.clear_roi_btn.clicked.connect(self._clear_roi)
        roi_controls_layout.addWidget(self.clear_roi_btn)

        roi_controls_layout.addStretch()

        preview_layout.addWidget(roi_controls)
        splitter.addWidget(preview_widget)

    def _create_button_bar(self, layout):
        """创建按钮栏"""
        button_bar = QWidget()
        button_bar.setFixedHeight(70)
        button_bar.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-top: 1px solid {THEME_COLORS["dark_border_primary"]};
            }}
        """)

        button_layout = QVBoxLayout(button_bar)
        button_layout.setContentsMargins(15, 8, 15, 8)
        button_layout.setSpacing(5)

        # 第一行：特殊功能按钮
        special_layout = QHBoxLayout()

        # 模板相关按钮
        self.template_create_btn = ModernButton("📷 创建模板", ModernButton.INFO)
        self.template_create_btn.clicked.connect(self._create_template)
        self.template_create_btn.setVisible(False)  # 默认隐藏，根据算法类型显示
        special_layout.addWidget(self.template_create_btn)

        self.template_load_btn = ModernButton("📁 加载模板", ModernButton.SECONDARY)
        self.template_load_btn.clicked.connect(self._load_template_image)
        self.template_load_btn.setVisible(False)
        special_layout.addWidget(self.template_load_btn)

        # 颜色相关按钮
        self.color_pick_btn = ModernButton("🎨 选择颜色", ModernButton.WARNING)
        self.color_pick_btn.clicked.connect(self._pick_color)
        self.color_pick_btn.setVisible(False)
        special_layout.addWidget(self.color_pick_btn)

        self.color_range_btn = ModernButton("🌈 颜色范围", ModernButton.WARNING)
        self.color_range_btn.clicked.connect(self._set_color_range)
        self.color_range_btn.setVisible(False)
        special_layout.addWidget(self.color_range_btn)

        # 训练相关按钮
        self.train_btn = ModernButton("🧠 训练模型", ModernButton.SUCCESS)
        self.train_btn.clicked.connect(self._train_model)
        self.train_btn.setVisible(False)
        special_layout.addWidget(self.train_btn)

        special_layout.addStretch()
        button_layout.addLayout(special_layout)

        # 第二行：主要按钮
        main_layout = QHBoxLayout()

        # 预览按钮
        preview_btn = ModernButton("👁️ 预览", ModernButton.INFO)
        preview_btn.clicked.connect(self._preview_algorithm)
        main_layout.addWidget(preview_btn)

        main_layout.addStretch()

        # 控制按钮
        reset_btn = ModernButton("重置", ModernButton.SECONDARY)
        reset_btn.clicked.connect(self._reset_config)
        main_layout.addWidget(reset_btn)

        apply_btn = ModernButton("应用", ModernButton.SUCCESS)
        apply_btn.clicked.connect(self._apply_config)
        main_layout.addWidget(apply_btn)

        ok_btn = ModernButton("确定", ModernButton.PRIMARY)
        ok_btn.clicked.connect(self.accept)
        main_layout.addWidget(ok_btn)

        cancel_btn = ModernButton("取消", ModernButton.SECONDARY)
        cancel_btn.clicked.connect(self.reject)
        main_layout.addWidget(cancel_btn)

        button_layout.addLayout(main_layout)
        layout.addWidget(button_bar)





    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "algorithm": "",
            "parameters": {},
            "roi_regions": [],
            "input_enabled": True,
            "output_enabled": True,
            "display_options": {
                "show_roi": True,
                "show_results": True,
                "overlay_color": THEME_COLORS["success"]
            }
        }

    def _load_algorithm_config(self):
        """加载算法配置"""
        try:
            # 根据节点类型自动选择算法
            node_type = getattr(self.node, 'node_type', 'unknown')
            node_title = getattr(self.node, 'title', '')

            # 节点类型到算法的映射
            node_algorithm_mapping = {
                'input': {
                    '相机输入': 'image_source.camera',
                    '文件输入': 'image_source.file',
                    '视频输入': 'image_source.video',
                    '网络输入': 'image_source.network'
                },
                'processing': {
                    'Canny边缘': 'image_processing.edge_detection',
                    '高斯模糊': 'image_processing.gaussian_blur',
                    '中值滤波': 'image_processing.median_blur',
                    '双边滤波': 'image_processing.bilateral_filter',
                    '形态学操作': 'image_processing.morphology',
                    '阈值处理': 'image_processing.threshold',
                    '颜色空间转换': 'image_processing.color_space',
                    '直方图处理': 'image_processing.histogram',
                    '对比度调整': 'image_processing.contrast',
                    '降噪处理': 'image_processing.noise_reduction'
                },
                'detection': {
                    '模板匹配': 'feature_detection.template_matching',
                    '角点检测': 'feature_detection.corner_detection',
                    '斑点检测': 'feature_detection.blob_detection',
                    '直线检测': 'feature_detection.line_detection',
                    '圆形检测': 'feature_detection.circle_detection',
                    '轮廓检测': 'feature_detection.contour_detection',
                    '关键点检测': 'feature_detection.keypoint_detection'
                },
                'object_detection': {
                    '颜色检测': 'object_detection.color_detection',
                    '形状检测': 'object_detection.shape_detection',
                    '文本检测': 'object_detection.text_detection',
                    '条码检测': 'object_detection.barcode_detection',
                    '人脸检测': 'object_detection.face_detection'
                },
                'measurement': {
                    '距离测量': 'measurement.distance_measurement',
                    '角度测量': 'measurement.angle_measurement',
                    '面积测量': 'measurement.area_measurement',
                    '几何分析': 'measurement.geometry_analysis',
                    '尺寸测量': 'measurement.dimension_measurement'
                },
                'deep_learning': {
                    'YOLO检测': 'deep_learning.yolo_detection',
                    '图像分类': 'deep_learning.classification',
                    '语义分割': 'deep_learning.segmentation',
                    '姿态估计': 'deep_learning.pose_estimation'
                },
                'position_correction': {
                    '仿射变换': 'position_correction.affine_transform',
                    '透视变换': 'position_correction.perspective_transform',
                    '旋转修正': 'position_correction.rotation_correction',
                    '平移修正': 'position_correction.translation_correction',
                    '缩放修正': 'position_correction.scale_correction'
                }
            }

            # 填充算法下拉框
            self.algorithm_combo.clear()
            self.algorithm_combo.addItem("选择算法...", "")

            # 根据节点类型添加相关算法
            algorithm_count = 0
            selected_algorithm = None

            # 首先尝试根据节点标题直接匹配
            for category, algorithms in node_algorithm_mapping.items():
                if node_title in algorithms:
                    selected_algorithm = algorithms[node_title]
                    display_name = f"{node_title}"
                    self.algorithm_combo.addItem(display_name, selected_algorithm)
                    self.algorithm_combo.setCurrentIndex(1)  # 选择刚添加的算法
                    algorithm_count += 1
                    logger.info(f"根据节点标题自动选择算法: {node_title} -> {selected_algorithm}")
                    break

            # 如果没有直接匹配，添加该类型的所有算法
            if not selected_algorithm and node_type in node_algorithm_mapping:
                for title, algorithm_name in node_algorithm_mapping[node_type].items():
                    display_name = f"{title}"
                    self.algorithm_combo.addItem(display_name, algorithm_name)
                    algorithm_count += 1

            # 如果还是没有找到，添加所有算法
            if algorithm_count == 0:
                for category, algorithms in node_algorithm_mapping.items():
                    for title, algorithm_name in algorithms.items():
                        display_name = f"{title} ({category})"
                        self.algorithm_combo.addItem(display_name, algorithm_name)
                        algorithm_count += 1

            logger.info(f"已加载 {algorithm_count} 个算法到配置对话框")

            # 如果找到了匹配的算法，设置选中状态但不立即加载（避免重复）
            if selected_algorithm:
                # 设置算法选择，这会自动触发 currentIndexChanged 信号
                for i in range(self.algorithm_combo.count()):
                    if self.algorithm_combo.itemData(i) == selected_algorithm:
                        self.algorithm_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            logger.error(f"加载算法配置失败: {e}")
            # 添加一些默认算法选项
            self.algorithm_combo.addItem("相机输入", "image_source.camera")
            self.algorithm_combo.addItem("边缘检测", "image_processing.edge_detection")
            self.algorithm_combo.addItem("高斯模糊", "image_processing.gaussian_blur")

    def _on_algorithm_changed(self, algorithm_display_name: str):
        """算法选择改变事件"""
        algorithm_name = self.algorithm_combo.currentData()

        if not algorithm_name:
            self._hide_all_special_buttons()
            return

        try:
            # 清除之前的参数界面
            self._clear_current_algorithm_ui()

            # 根据算法类型显示相应的特殊功能按钮
            self._update_special_buttons(algorithm_name)

            # 使用新的算法配置系统
            if AlgorithmConfigWidgetFactory:
                algorithm_widget = AlgorithmConfigWidgetFactory.create_config_widget(algorithm_name, self)

                if algorithm_widget:
                    # 设置可用的输入源
                    input_sources = self._get_available_input_sources()
                    algorithm_widget.set_input_sources(input_sources)

                    # 添加到新的参数配置容器
                    self.param_content_layout.addWidget(algorithm_widget)
                    self.param_content_layout.addStretch()
                    self.current_algorithm_ui = algorithm_widget

                    # 连接参数改变信号
                    algorithm_widget.parameter_changed.connect(self._on_parameter_changed)
                    algorithm_widget.preview_requested.connect(self._on_preview_requested)

                    logger.info(f"已加载专用算法配置界面: {algorithm_name}")
                else:
                    # 创建简单的参数界面
                    self._create_simple_parameter_ui(algorithm_name)
            else:
                # 创建简单的参数界面
                self._create_simple_parameter_ui(algorithm_name)

        except Exception as e:
            logger.error(f"切换算法失败: {e}")
            QMessageBox.warning(self, "错误", f"切换算法失败: {str(e)}")

    def _update_special_buttons(self, algorithm_name: str):
        """根据算法类型更新特殊功能按钮的显示"""
        # 先隐藏所有特殊按钮
        self._hide_all_special_buttons()

        # 根据算法类型显示相应按钮
        if 'template_matching' in algorithm_name:
            # 模板匹配算法
            self.template_create_btn.setVisible(True)
            self.template_load_btn.setVisible(True)

        elif 'color_detection' in algorithm_name:
            # 颜色检测算法
            self.color_pick_btn.setVisible(True)
            self.color_range_btn.setVisible(True)

        elif any(keyword in algorithm_name for keyword in ['yolo', 'classification', 'segmentation', 'pose_estimation']):
            # 深度学习算法
            self.train_btn.setVisible(True)

        elif 'face_detection' in algorithm_name or 'text_detection' in algorithm_name:
            # 需要训练的检测算法
            self.train_btn.setVisible(True)

    def _hide_all_special_buttons(self):
        """隐藏所有特殊功能按钮"""
        self.template_create_btn.setVisible(False)
        self.template_load_btn.setVisible(False)
        self.color_pick_btn.setVisible(False)
        self.color_range_btn.setVisible(False)
        self.train_btn.setVisible(False)

    def _clear_current_algorithm_ui(self):
        """清除当前算法配置界面"""
        if hasattr(self, 'current_algorithm_ui') and self.current_algorithm_ui:
            self.current_algorithm_ui.setParent(None)
            self.current_algorithm_ui.deleteLater()
            self.current_algorithm_ui = None

        # 清空参数配置容器
        for i in reversed(range(self.param_content_layout.count())):
            child = self.param_content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                child.deleteLater()

    def _on_resize_event(self, event):
        """窗口大小变化事件"""
        if hasattr(self, 'original_resize_event'):
            self.original_resize_event(event)

        # 根据窗口大小调整分割比例
        if hasattr(self, 'main_splitter'):
            total_width = self.width()
            if total_width > 0:
                # 计算合适的分割比例
                left_width = max(350, min(500, int(total_width * 0.4)))
                right_width = total_width - left_width - 10  # 减去分割器宽度

                self.main_splitter.setSizes([left_width, right_width])

    def _get_available_input_sources(self) -> List[Dict[str, Any]]:
        """获取可用的输入源"""
        input_sources = []

        try:
            # 从工作流编辑器获取连接信息
            if hasattr(self, 'workflow_editor') and self.workflow_editor:
                # 获取当前节点的输入连接
                current_node_id = getattr(self.node, 'node_id', None)
                if current_node_id:
                    # 查找连接到当前节点的其他节点
                    for connection in self.workflow_editor.connections:
                        if hasattr(connection, 'end_node') and connection.end_node.node_id == current_node_id:
                            source_node = connection.start_node
                            input_sources.append({
                                'node_id': source_node.node_id,
                                'node_name': getattr(source_node, 'title', source_node.node_id),
                                'output_name': 'image',  # 默认输出名称
                                'data_type': 'image'
                            })

            # 如果没有找到连接的输入源，添加一些默认选项
            if not input_sources:
                input_sources = [
                    {
                        'node_id': 'camera_input',
                        'node_name': '相机输入',
                        'output_name': 'image',
                        'data_type': 'image'
                    },
                    {
                        'node_id': 'file_input',
                        'node_name': '文件输入',
                        'output_name': 'image',
                        'data_type': 'image'
                    }
                ]

        except Exception as e:
            logger.error(f"获取输入源失败: {e}")

        return input_sources

    def _on_preview_requested(self):
        """处理预览请求"""
        try:
            logger.info("算法预览请求")

            if hasattr(self, 'current_algorithm_ui') and self.current_algorithm_ui:
                parameters = self.current_algorithm_ui.get_parameters()
                logger.info(f"当前算法参数: {parameters}")

                # 获取算法名称
                algorithm_name = self.algorithm_combo.currentData()

                # 根据算法类型执行不同的预览逻辑
                if algorithm_name == "image_source.camera":
                    self._preview_camera(parameters)
                elif algorithm_name == "image_processing.edge_detection":
                    self._preview_edge_detection(parameters)
                elif algorithm_name == "image_processing.gaussian_blur":
                    self._preview_gaussian_blur(parameters)
                else:
                    self._preview_generic_algorithm(algorithm_name)

        except Exception as e:
            logger.error(f"预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"预览失败: {str(e)}")

    def _preview_camera(self, parameters: Dict[str, Any]):
        """增强版相机预览 - 修复黑屏问题"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage
            import time

            camera_index = parameters.get("camera_index", 0)
            width = parameters.get("width", 640)
            height = parameters.get("height", 480)
            fps = parameters.get("fps", 30)

            logger.info(f"开始相机预览: 设备{camera_index}, 分辨率{width}x{height}")

            # 尝试打开相机 - 增强版本
            cap = cv2.VideoCapture(camera_index)
            if not cap.isOpened():
                # 尝试不同的后端
                backends = [cv2.CAP_AVFOUNDATION, cv2.CAP_DSHOW, cv2.CAP_V4L2]
                for backend in backends:
                    try:
                        cap = cv2.VideoCapture(camera_index, backend)
                        if cap.isOpened():
                            logger.info(f"使用后端 {backend} 成功打开相机")
                            break
                    except:
                        continue

                if not cap.isOpened():
                    self._show_camera_error("无法打开相机", f"相机设备 {camera_index} 不可用")
                    return

            try:
                # 设置相机参数
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
                cap.set(cv2.CAP_PROP_FPS, fps)

                # 设置缓冲区大小为1，减少延迟
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 相机预热 - 解决黑屏问题的关键
                logger.info("相机预热中...")
                for i in range(5):
                    ret, warmup_frame = cap.read()
                    if ret and warmup_frame is not None:
                        mean_val = np.mean(warmup_frame)
                        logger.debug(f"预热帧 {i+1}/5: 均值={mean_val:.1f}")
                        if mean_val > 10:  # 找到有效图像
                            logger.info(f"预热完成，第{i+1}帧有效")
                            break
                    time.sleep(0.1)  # 短暂等待

                # 获取最终预览帧
                ret, frame = cap.read()

                if ret and frame is not None:
                    # 验证图像质量
                    mean_val = np.mean(frame)
                    logger.info(f"捕获图像: 尺寸={frame.shape}, 均值={mean_val:.1f}")

                    if mean_val < 5:  # 图像太暗
                        logger.warning("图像太暗，尝试调整相机参数")
                        # 尝试调整亮度和对比度
                        cap.set(cv2.CAP_PROP_BRIGHTNESS, 0.5)
                        cap.set(cv2.CAP_PROP_CONTRAST, 0.5)
                        cap.set(cv2.CAP_PROP_EXPOSURE, -1)  # 自动曝光

                        # 重新捕获
                        time.sleep(0.2)
                        ret, frame = cap.read()

                        if ret and frame is not None:
                            mean_val = np.mean(frame)
                            logger.info(f"调整后图像均值: {mean_val:.1f}")

                    # 确保数据连续性
                    if not frame.flags['C_CONTIGUOUS']:
                        frame = np.ascontiguousarray(frame)
                        logger.debug("确保图像数据连续性")

                    # 转换颜色空间 BGR -> RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # 创建QImage
                    h, w, ch = frame_rgb.shape
                    bytes_per_line = ch * w
                    q_image = QImage(frame_rgb.data, w, h, bytes_per_line, QImage.Format_RGB888)

                    # 验证QImage有效性
                    if q_image.isNull():
                        logger.error("QImage创建失败")
                        self._show_camera_error("图像转换失败", "无法创建有效的图像对象")
                        return

                    # 转换为QPixmap
                    pixmap = QPixmap.fromImage(q_image)
                    if pixmap.isNull():
                        logger.error("QPixmap创建失败")
                        self._show_camera_error("图像显示失败", "无法创建显示对象")
                        return

                    # 更新预览图像 - 使用增强的显示方法
                    self._show_preview_result(frame, "相机实时预览")
                    logger.info(f"相机预览更新成功: {w}x{h}, 均值={mean_val:.1f}")

                else:
                    logger.error("无法读取相机图像")
                    self._show_camera_error("读取失败", "无法从相机获取图像数据")

            finally:
                cap.release()
                logger.debug("相机资源已释放")

        except Exception as e:
            logger.error(f"相机预览失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            self._show_camera_error("预览失败", f"相机预览出现错误: {str(e)}")

    def _show_camera_error(self, title: str, message: str):
        """显示相机错误信息"""
        try:
            import cv2
            import numpy as np

            # 创建错误提示图像
            error_image = np.zeros((480, 640, 3), dtype=np.uint8)
            error_image[:] = [40, 40, 40]  # 深灰色背景

            # 添加错误图标和文字
            cv2.putText(error_image, "Camera Error", (200, 200),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 255), 2)
            cv2.putText(error_image, title, (220, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(error_image, message[:40], (150, 280),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)

            # 添加重试提示
            cv2.putText(error_image, "Click 'Preview Camera' to retry", (160, 320),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (100, 200, 255), 1)

            # 显示错误图像
            self._show_preview_result(error_image, f"相机错误: {title}")

        except Exception as e:
            logger.error(f"显示相机错误信息失败: {e}")
            # 最后的备用方案
            QMessageBox.warning(self, title, message)

    def _start_live_camera_preview(self, parameters: Dict[str, Any]):
        """启动实时相机预览 - 解决黑屏问题的终极方案"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtCore import QTimer
            from PyQt5.QtGui import QImage

            camera_index = parameters.get("camera_index", 0)
            width = parameters.get("width", 640)
            height = parameters.get("height", 480)

            # 停止之前的预览
            if hasattr(self, 'camera_timer') and self.camera_timer.isActive():
                self.camera_timer.stop()
            if hasattr(self, 'camera_cap') and self.camera_cap.isOpened():
                self.camera_cap.release()

            # 创建相机对象
            self.camera_cap = cv2.VideoCapture(camera_index)
            if not self.camera_cap.isOpened():
                # 尝试不同后端
                backends = [cv2.CAP_AVFOUNDATION, cv2.CAP_DSHOW, cv2.CAP_V4L2]
                for backend in backends:
                    try:
                        self.camera_cap = cv2.VideoCapture(camera_index, backend)
                        if self.camera_cap.isOpened():
                            logger.info(f"使用后端 {backend} 成功打开相机")
                            break
                    except:
                        continue

                if not self.camera_cap.isOpened():
                    self._show_camera_error("无法打开相机", f"相机设备 {camera_index} 不可用")
                    return

            # 设置相机参数
            self.camera_cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.camera_cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            self.camera_cap.set(cv2.CAP_PROP_FPS, 30)
            self.camera_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # 创建定时器进行实时更新
            self.camera_timer = QTimer()
            self.camera_timer.timeout.connect(self._update_camera_frame)
            self.camera_timer.start(33)  # 约30FPS

            logger.info("实时相机预览已启动")

        except Exception as e:
            logger.error(f"启动实时相机预览失败: {e}")
            self._show_camera_error("启动失败", f"无法启动实时预览: {str(e)}")

    def _update_camera_frame(self):
        """更新相机帧"""
        try:
            import numpy as np

            if not hasattr(self, 'camera_cap') or not self.camera_cap.isOpened():
                return

            ret, frame = self.camera_cap.read()
            if ret and frame is not None:
                # 确保数据连续性
                if not frame.flags['C_CONTIGUOUS']:
                    frame = np.ascontiguousarray(frame)

                # 显示帧
                self._show_preview_result(frame, "实时相机预览")

        except Exception as e:
            logger.error(f"更新相机帧失败: {e}")
            # 停止预览
            if hasattr(self, 'camera_timer'):
                self.camera_timer.stop()

    def _stop_camera_preview(self):
        """停止相机预览"""
        try:
            if hasattr(self, 'camera_timer') and self.camera_timer.isActive():
                self.camera_timer.stop()
                logger.info("相机预览定时器已停止")

            if hasattr(self, 'camera_cap') and self.camera_cap.isOpened():
                self.camera_cap.release()
                logger.info("相机资源已释放")

        except Exception as e:
            logger.error(f"停止相机预览失败: {e}")

    def closeEvent(self, event):
        """对话框关闭事件"""
        try:
            # 停止相机预览
            self._stop_camera_preview()

            # 调用父类关闭事件
            super().closeEvent(event)

        except Exception as e:
            logger.error(f"关闭对话框时发生错误: {e}")
            super().closeEvent(event)

    def _preview_edge_detection(self, parameters: Dict[str, Any]):
        """预览边缘检测"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage

            # 获取当前预览图像
            current_pixmap = self.roi_view.get_current_image()
            if not current_pixmap:
                self._load_preview_image()
                current_pixmap = self.roi_view.get_current_image()

            if current_pixmap:
                # 转换为OpenCV格式
                q_image = current_pixmap.toImage()
                width = q_image.width()
                height = q_image.height()
                ptr = q_image.bits()
                ptr.setsize(q_image.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                image = cv2.cvtColor(arr, cv2.COLOR_RGBA2BGR)

                # 应用边缘检测
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

                # 应用高斯模糊（如果启用）
                if parameters.get("gaussian_blur", True):
                    blur_kernel = parameters.get("blur_kernel", 5)
                    gray = cv2.GaussianBlur(gray, (blur_kernel, blur_kernel), 0)

                # Canny边缘检测
                low_threshold = parameters.get("low_threshold", 50)
                high_threshold = parameters.get("high_threshold", 150)
                aperture_size = parameters.get("aperture_size", 3)
                l2_gradient = parameters.get("l2_gradient", False)

                edges = cv2.Canny(gray, low_threshold, high_threshold,
                                apertureSize=aperture_size, L2gradient=l2_gradient)

                # 转换回彩色图像用于显示
                edges_color = cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)

                # 转换为QPixmap
                height, width, _ = edges_color.shape
                bytes_per_line = 3 * width
                q_image = QImage(edges_color.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览图像
                self.roi_view.set_image(pixmap)
                logger.info("边缘检测预览更新成功")

        except Exception as e:
            logger.error(f"边缘检测预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"边缘检测预览失败: {str(e)}")

    def _preview_gaussian_blur(self, parameters: Dict[str, Any]):
        """预览高斯模糊"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage

            # 获取当前预览图像
            current_pixmap = self.roi_view.get_current_image()
            if not current_pixmap:
                self._load_preview_image()
                current_pixmap = self.roi_view.get_current_image()

            if current_pixmap:
                # 转换为OpenCV格式
                q_image = current_pixmap.toImage()
                width = q_image.width()
                height = q_image.height()
                ptr = q_image.bits()
                ptr.setsize(q_image.byteCount())
                arr = np.array(ptr).reshape(height, width, 4)  # RGBA
                image = cv2.cvtColor(arr, cv2.COLOR_RGBA2BGR)

                # 应用高斯模糊
                kernel_x = parameters.get("kernel_size_x", 15)
                kernel_y = parameters.get("kernel_size_y", 15)
                sigma_x = parameters.get("sigma_x", 0.0)
                sigma_y = parameters.get("sigma_y", 0.0)

                # 确保核大小为奇数
                kernel_x = kernel_x if kernel_x % 2 == 1 else kernel_x + 1
                kernel_y = kernel_y if kernel_y % 2 == 1 else kernel_y + 1

                blurred = cv2.GaussianBlur(image, (kernel_x, kernel_y), sigma_x, sigmaY=sigma_y)

                # 转换为RGB
                blurred_rgb = cv2.cvtColor(blurred, cv2.COLOR_BGR2RGB)

                # 转换为QPixmap
                height, width, _ = blurred_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(blurred_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览图像
                self.roi_view.set_image(pixmap)
                logger.info("高斯模糊预览更新成功")

        except Exception as e:
            logger.error(f"高斯模糊预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"高斯模糊预览失败: {str(e)}")

    def _preview_generic_algorithm(self, algorithm_name: str):
        """通用算法预览"""
        try:
            import cv2
            import numpy as np

            # 确保有测试图像
            if not hasattr(self, 'current_test_image') or self.current_test_image is None:
                logger.warning("没有测试图像，创建默认图像")
                self._create_test_image()

            if self.current_test_image is None:
                logger.error("无法创建测试图像")
                return

            # 创建一个简单的处理效果作为演示
            result_image = self.current_test_image.copy()

            logger.debug(f"通用预览输入图像: 形状={result_image.shape}, 类型={result_image.dtype}")

            # 根据算法名称应用不同的效果
            if 'blur' in algorithm_name.lower():
                result_image = cv2.GaussianBlur(result_image, (15, 15), 0)
                logger.debug("应用模糊效果")
            elif 'edge' in algorithm_name.lower():
                gray = cv2.cvtColor(result_image, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 100, 200)
                result_image = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
                logger.debug("应用边缘检测效果")
            elif 'threshold' in algorithm_name.lower():
                gray = cv2.cvtColor(result_image, cv2.COLOR_BGR2GRAY)
                _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
                result_image = cv2.cvtColor(thresh, cv2.COLOR_GRAY2BGR)
                logger.debug("应用阈值处理效果")
            elif 'color' in algorithm_name.lower():
                # 颜色相关算法 - 增强饱和度
                try:
                    hsv = cv2.cvtColor(result_image, cv2.COLOR_BGR2HSV)
                    hsv[:, :, 1] = np.clip(hsv[:, :, 1] * 1.5, 0, 255).astype(np.uint8)
                    result_image = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)
                    logger.debug("应用颜色增强效果")
                except Exception as e:
                    logger.warning(f"颜色增强失败: {e}")
            elif 'detection' in algorithm_name.lower():
                # 检测算法 - 添加检测框
                h, w = result_image.shape[:2]
                cv2.rectangle(result_image, (w//4, h//4), (3*w//4, 3*h//4), (0, 255, 0), 3)
                cv2.putText(result_image, "Detected Object", (w//4, h//4-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                logger.debug("应用检测框效果")
            elif 'measurement' in algorithm_name.lower():
                # 测量算法 - 添加测量线
                h, w = result_image.shape[:2]
                cv2.line(result_image, (w//4, h//2), (3*w//4, h//2), (255, 0, 0), 2)
                cv2.putText(result_image, f"Length: {w//2} px", (w//4, h//2-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                logger.debug("应用测量线效果")
            else:
                # 默认添加一个边框表示处理过
                cv2.rectangle(result_image, (10, 10),
                             (result_image.shape[1]-10, result_image.shape[0]-10),
                             (0, 255, 255), 3)
                cv2.putText(result_image, f"Algorithm: {algorithm_name}", (20, 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
                logger.debug("应用默认边框效果")

            # 确保结果图像有效
            if result_image is None or result_image.size == 0:
                logger.error("处理后的图像无效")
                return

            logger.debug(f"通用预览输出图像: 形状={result_image.shape}, 类型={result_image.dtype}")

            # 显示结果
            self._show_preview_result(result_image, f"算法预览: {algorithm_name}")

        except Exception as e:
            logger.error(f"通用算法预览失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            QMessageBox.warning(self, "预览失败", f"算法预览失败: {str(e)}")

    def _create_simple_parameter_ui(self, algorithm_name: str):
        """创建简单的参数界面 - 增强UI一致性"""
        try:
            # 获取参数模式
            param_schema = {}

            if self.algorithm_registry:
                # 获取算法实例
                algorithm_class = self.algorithm_registry.get_algorithm(algorithm_name)
                if algorithm_class:
                    algorithm = algorithm_class()
                    param_schema = algorithm.get_parameter_schema()

            # 如果没有参数模式，使用默认参数
            if not param_schema:
                param_schema = self._get_default_parameter_schema(algorithm_name)

            # 创建参数控件容器 - 使用统一主题样式
            param_container = QWidget()
            param_container.setStyleSheet(theme_manager.get_card_style())
            container_layout = QVBoxLayout(param_container)
            container_layout.setContentsMargins(15, 15, 15, 15)
            container_layout.setSpacing(10)

            # 创建参数控件
            for param_name, param_config in param_schema.items():
                param_type = param_config.get("type", "string")
                description = param_config.get("description", param_name)
                default_value = param_config.get("default")

                # 创建参数行 - 使用统一主题样式
                param_row = QWidget()
                param_row.setStyleSheet(theme_manager.get_input_container_style())
                row_layout = QVBoxLayout(param_row)
                row_layout.setContentsMargins(8, 8, 8, 8)
                row_layout.setSpacing(5)

                # 参数标签 - 使用统一标签样式
                label = QLabel(description)
                label.setStyleSheet(theme_manager.get_label_style("secondary"))
                row_layout.addWidget(label)

                # 根据参数类型创建控件 - 使用统一主题样式
                if param_type == "int":
                    widget = QSpinBox()
                    widget.setRange(param_config.get("min", -999999), param_config.get("max", 999999))
                    if default_value is not None:
                        widget.setValue(default_value)
                    widget.setStyleSheet(theme_manager.get_spinbox_style())
                elif param_type == "float":
                    widget = QDoubleSpinBox()
                    widget.setRange(param_config.get("min", -999999.0), param_config.get("max", 999999.0))
                    widget.setDecimals(param_config.get("decimals", 2))
                    if default_value is not None:
                        widget.setValue(default_value)
                    widget.setStyleSheet(theme_manager.get_spinbox_style())
                elif param_type == "bool":
                    widget = QCheckBox("启用")
                    if default_value is not None:
                        widget.setChecked(default_value)
                    widget.setStyleSheet(theme_manager.get_checkbox_style())
                elif param_type == "choice":
                    widget = QComboBox()
                    choices = param_config.get("choices", [])
                    for choice in choices:
                        widget.addItem(str(choice))
                    if default_value is not None:
                        widget.setCurrentText(str(default_value))
                    widget.setStyleSheet(theme_manager.get_combobox_style())
                else:  # string
                    widget = QLineEdit()
                    if default_value is not None:
                        widget.setText(str(default_value))
                    widget.setStyleSheet(theme_manager.get_input_style())

                row_layout.addWidget(widget)
                container_layout.addWidget(param_row)

            # 添加到参数配置容器
            self.param_content_layout.addWidget(param_container)
            self.param_content_layout.addStretch()

        except Exception as e:
            logger.error(f"创建简单参数界面失败: {e}")

    def _get_default_parameter_schema(self, algorithm_name: str) -> Dict[str, Any]:
        """获取默认参数模式"""
        default_schemas = {
            "gaussian_blur": {
                "kernel_size": {"type": "int", "default": 5, "min": 1, "max": 31, "description": "核大小"},
                "sigma": {"type": "float", "default": 1.5, "min": 0.1, "max": 10.0, "description": "标准差"}
            },
            "canny_edge": {
                "threshold1": {"type": "int", "default": 100, "min": 0, "max": 255, "description": "低阈值"},
                "threshold2": {"type": "int", "default": 200, "min": 0, "max": 255, "description": "高阈值"}
            },
            "sobel_edge": {
                "ksize": {"type": "int", "default": 3, "min": 1, "max": 7, "description": "核大小"},
                "scale": {"type": "float", "default": 1.0, "min": 0.1, "max": 10.0, "description": "缩放因子"}
            },
            "contour_detection": {
                "mode": {"type": "choice", "default": "EXTERNAL", "choices": ["EXTERNAL", "LIST", "CCOMP", "TREE"], "description": "轮廓检索模式"},
                "method": {"type": "choice", "default": "SIMPLE", "choices": ["NONE", "SIMPLE", "TC89_L1", "TC89_KCOS"], "description": "轮廓近似方法"}
            },
            "template_matching": {
                "method": {"type": "choice", "default": "TM_CCOEFF_NORMED", "choices": ["TM_CCOEFF", "TM_CCOEFF_NORMED", "TM_CCORR", "TM_CCORR_NORMED"], "description": "匹配方法"},
                "threshold": {"type": "float", "default": 0.8, "min": 0.0, "max": 1.0, "description": "匹配阈值"}
            }
        }

        return default_schemas.get(algorithm_name, {
            "enabled": {"type": "bool", "default": True, "description": "启用算法"}
        })

    def _on_parameter_changed(self, param_name: str, value: Any):
        """参数改变事件"""
        if "parameters" not in self.config_data:
            self.config_data["parameters"] = {}

        self.config_data["parameters"][param_name] = value
        logger.debug(f"参数已更新: {param_name} = {value}")

    def _toggle_roi_drawing(self):
        """切换ROI绘制模式"""
        if self.roi_view.drawing_mode:
            self.roi_view.stop_roi_drawing()
            self.draw_roi_btn.setText("绘制ROI")
        else:
            self.roi_view.start_roi_drawing()
            self.draw_roi_btn.setText("停止绘制")

    def _clear_roi(self):
        """清除所有ROI"""
        self.roi_view.scene.clear()
        self.roi_view.roi_items.clear()
        self.roi_regions.clear()
        self.roi_list.clear()

        # 重新加载背景图像
        self._load_preview_image()

    def _on_roi_created(self, rect: QRectF):
        """ROI创建事件"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": int(rect.x()),
            "y": int(rect.y()),
            "width": int(rect.width()),
            "height": int(rect.height()),
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 选中新创建的ROI
        self.roi_list.setCurrentItem(item)

        logger.info(f"创建ROI: {roi_data}")

    def _on_roi_selection_changed(self):
        """ROI选择改变事件"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            roi_data = self.roi_regions[roi_index]

            # 更新ROI属性控件
            self.roi_name_edit.setText(roi_data["name"])
            self.roi_x_spin.setValue(roi_data["x"])
            self.roi_y_spin.setValue(roi_data["y"])
            self.roi_w_spin.setValue(roi_data["width"])
            self.roi_h_spin.setValue(roi_data["height"])

    def _add_roi(self):
        """添加ROI"""
        roi_data = {
            "name": f"ROI_{len(self.roi_regions) + 1}",
            "x": 50,
            "y": 50,
            "width": 100,
            "height": 100,
            "type": "rectangle"
        }

        self.roi_regions.append(roi_data)

        # 添加到列表
        item = QListWidgetItem(roi_data["name"])
        item.setData(Qt.UserRole, len(self.roi_regions) - 1)
        self.roi_list.addItem(item)

        # 在视图中添加ROI
        rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
        roi_rect = self.roi_view.scene.addRect(
            rect,
            QPen(QColor(THEME_COLORS["success"]), 2),
            QBrush(QColor(THEME_COLORS["success"]).lighter(150))
        )
        roi_rect.setOpacity(0.5)
        self.roi_view.roi_items.append(roi_rect)

        # 选中新添加的ROI
        self.roi_list.setCurrentItem(item)

    def _delete_roi(self):
        """删除选中的ROI"""
        current_item = self.roi_list.currentItem()
        if not current_item:
            return

        roi_index = current_item.data(Qt.UserRole)
        if roi_index is not None and 0 <= roi_index < len(self.roi_regions):
            # 从数据中删除
            del self.roi_regions[roi_index]

            # 从列表中删除
            self.roi_list.takeItem(self.roi_list.row(current_item))

            # 重新绘制ROI
            self._redraw_roi()

    def _redraw_roi(self):
        """重新绘制所有ROI"""
        # 清除现有ROI
        for item in self.roi_view.roi_items:
            self.roi_view.scene.removeItem(item)
        self.roi_view.roi_items.clear()

        # 重新绘制
        for roi_data in self.roi_regions:
            rect = QRectF(roi_data["x"], roi_data["y"], roi_data["width"], roi_data["height"])
            roi_rect = self.roi_view.scene.addRect(
                rect,
                QPen(QColor(THEME_COLORS["success"]), 2),
                QBrush(QColor(THEME_COLORS["success"]).lighter(150))
            )
            roi_rect.setOpacity(0.5)
            self.roi_view.roi_items.append(roi_rect)

    def _load_preview_image(self):
        """加载预览图像"""
        try:
            # 尝试从项目中加载一个示例图像
            import os
            import numpy as np
            import cv2
            from PyQt5.QtGui import QImage

            # 查找示例图像文件
            sample_image_paths = [
                "assets/sample_image.jpg",
                "assets/test_image.png",
                "docs/sample.jpg",
                "test_data/sample.png"
            ]

            loaded = False
            for image_path in sample_image_paths:
                if os.path.exists(image_path):
                    try:
                        # 使用OpenCV加载图像
                        image = cv2.imread(image_path)
                        if image is not None:
                            # 转换为RGB
                            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                            # 调整大小到合适的预览尺寸
                            height, width = image_rgb.shape[:2]
                            if width > 800 or height > 600:
                                scale = min(800/width, 600/height)
                                new_width = int(width * scale)
                                new_height = int(height * scale)
                                image_rgb = cv2.resize(image_rgb, (new_width, new_height))

                            # 转换为QPixmap
                            height, width, _ = image_rgb.shape
                            bytes_per_line = 3 * width
                            q_image = QImage(image_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
                            pixmap = QPixmap.fromImage(q_image)

                            self.roi_view.set_image(pixmap)
                            loaded = True
                            logger.info(f"加载示例图像: {image_path}")
                            break
                    except Exception as e:
                        logger.warning(f"加载图像 {image_path} 失败: {e}")
                        continue

            # 如果没有找到示例图像，创建一个测试图像
            if not loaded:
                self._create_test_image()

        except Exception as e:
            logger.error(f"加载预览图像失败: {e}")
            self._create_test_image()

    def _create_test_image(self):
        """创建测试图像"""
        try:
            import numpy as np
            import cv2
            from PyQt5.QtGui import QImage

            # 创建一个更有趣的测试图像
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)

            # 创建渐变背景
            for y in range(480):
                for x in range(640):
                    test_image[y, x] = [
                        int(64 + (x / 640) * 64),  # R
                        int(64 + (y / 480) * 64),  # G
                        int(128 - (x / 640) * 64)  # B
                    ]

            # 添加一些几何图形用于测试
            # 绘制矩形
            cv2.rectangle(test_image, (100, 100), (200, 200), (255, 255, 255), 2)
            cv2.rectangle(test_image, (120, 120), (180, 180), (0, 255, 0), -1)

            # 绘制圆形
            cv2.circle(test_image, (400, 150), 50, (255, 0, 0), 2)
            cv2.circle(test_image, (400, 150), 30, (0, 0, 255), -1)

            # 绘制线条
            cv2.line(test_image, (50, 300), (590, 300), (255, 255, 0), 3)
            cv2.line(test_image, (320, 50), (320, 430), (255, 0, 255), 3)

            # 添加文字
            cv2.putText(test_image, "WireVision Test Image", (150, 350),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(test_image, "Algorithm Preview", (200, 400),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (200, 200, 200), 2)

            # 转换为QPixmap - 修复BGR到RGB通道问题
            height, width, _ = test_image.shape
            # OpenCV使用BGR格式，需要转换为RGB
            rgb_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
            bytes_per_line = 3 * width
            q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)

            # 确保图像不为空
            if not pixmap.isNull():
                self.roi_view.set_image(pixmap)
                # 保存当前测试图像供算法使用
                self.current_test_image = test_image.copy()
                logger.info(f"创建测试图像成功: {width}x{height}")
            else:
                logger.error("创建的QPixmap为空")
                self._create_fallback_image()

        except Exception as e:
            logger.error(f"创建测试图像失败: {e}")
            self._create_fallback_image()

    def _create_fallback_image(self):
        """创建备用图像"""
        try:
            import numpy as np
            from PyQt5.QtGui import QColor, QPainter, QFont, QLinearGradient

            pixmap = QPixmap(640, 480)
            bg_color = QColor(THEME_COLORS["dark_bg_card"])
            pixmap.fill(bg_color)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # 创建渐变背景
            gradient = QLinearGradient(0, 0, 640, 480)
            gradient.setColorAt(0, QColor(THEME_COLORS["dark_bg_content"]))
            gradient.setColorAt(1, QColor(THEME_COLORS["dark_bg_card"]))
            painter.fillRect(0, 0, 640, 480, gradient)

            # 绘制文本
            painter.setPen(QColor(THEME_COLORS["text_secondary"]))
            painter.setFont(QFont("Arial", 16))
            painter.drawText(200, 240, "预览图像加载中...")
            painter.end()

            self.roi_view.set_image(pixmap)
            # 创建对应的numpy数组
            self.current_test_image = np.zeros((480, 640, 3), dtype=np.uint8)
            # 使用主题背景色
            bg_rgb = QColor(THEME_COLORS["dark_bg_card"])
            self.current_test_image[:] = [bg_rgb.blue(), bg_rgb.green(), bg_rgb.red()]  # BGR格式
            logger.info("使用备用图像")
        except Exception as e:
            logger.error(f"创建备用图像失败: {e}")

    def _debug_roi_view(self):
        """调试ROI视图状态"""
        try:
            logger.debug("=== ROI视图调试信息 ===")
            logger.debug(f"ROI视图可见: {self.roi_view.isVisible()}")
            logger.debug(f"ROI视图尺寸: {self.roi_view.size()}")
            logger.debug(f"场景矩形: {self.roi_view.scene.sceneRect()}")
            logger.debug(f"场景项目数量: {len(self.roi_view.scene.items())}")
            logger.debug(f"视图变换: {self.roi_view.transform()}")
            logger.debug(f"当前图像项: {self.roi_view.current_pixmap_item}")

            if self.roi_view.current_pixmap_item:
                logger.debug(f"图像项可见: {self.roi_view.current_pixmap_item.isVisible()}")
                logger.debug(f"图像项位置: {self.roi_view.current_pixmap_item.pos()}")
                logger.debug(f"图像项Z值: {self.roi_view.current_pixmap_item.zValue()}")
                logger.debug(f"图像项边界: {self.roi_view.current_pixmap_item.boundingRect()}")

            # 强制重绘
            self.roi_view.viewport().update()
            self.roi_view.update()

            logger.debug("=== ROI视图调试完成 ===")

        except Exception as e:
            logger.error(f"调试ROI视图时发生错误: {e}")

    def _load_template(self):
        """加载参数模板"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "加载参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)

                # 应用模板数据
                self.config_data.update(template_data)
                self._apply_config_to_ui()

                QMessageBox.information(self, "成功", "参数模板已加载")

        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.warning(self, "错误", f"加载模板失败: {str(e)}")

    def _save_template(self):
        """保存参数模板"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存参数模板", "", "JSON文件 (*.json)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(self, "成功", "参数模板已保存")

        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            QMessageBox.warning(self, "错误", f"保存模板失败: {str(e)}")

    def _apply_config_to_ui(self):
        """将配置应用到UI"""
        try:
            # 应用算法选择
            algorithm_name = self.config_data.get("algorithm", "")
            if algorithm_name:
                for i in range(self.algorithm_combo.count()):
                    if self.algorithm_combo.itemData(i) == algorithm_name:
                        self.algorithm_combo.setCurrentIndex(i)
                        break

            # 应用ROI配置
            self.roi_regions = self.config_data.get("roi_regions", [])
            self._update_roi_list()
            self._redraw_roi()

            # 应用输入输出配置
            self.input_enabled.setChecked(self.config_data.get("input_enabled", True))
            self.output_enabled.setChecked(self.config_data.get("output_enabled", True))

        except Exception as e:
            logger.error(f"应用配置到UI失败: {e}")

    def _update_roi_list(self):
        """更新ROI列表"""
        self.roi_list.clear()

        for i, roi_data in enumerate(self.roi_regions):
            item = QListWidgetItem(roi_data["name"])
            item.setData(Qt.UserRole, i)
            self.roi_list.addItem(item)

    def _reset_config(self):
        """重置配置"""
        self.config_data = self._get_default_config()
        self._apply_config_to_ui()
        self._clear_roi()

    def _apply_config(self):
        """应用配置"""
        # 收集当前配置
        self._collect_current_config()

        # 发射配置改变信号
        self.config_changed.emit(self.config_data)

        QMessageBox.information(self, "成功", "配置已应用")

    def _collect_current_config(self):
        """收集当前配置"""
        # 收集算法配置
        self.config_data["algorithm"] = self.algorithm_combo.currentData() or ""

        # 收集参数配置
        if hasattr(self, 'current_algorithm_ui') and hasattr(self.current_algorithm_ui, 'get_parameters'):
            self.config_data["parameters"] = self.current_algorithm_ui.get_parameters()

        # 收集ROI配置
        self.config_data["roi_regions"] = self.roi_regions.copy()

        # 收集输入输出配置（默认启用）
        self.config_data["input_enabled"] = True
        self.config_data["output_enabled"] = True

    def accept(self):
        """确定按钮"""
        self._collect_current_config()
        self.config_changed.emit(self.config_data)
        super().accept()

    def showEvent(self, event):
        """显示事件"""
        super().showEvent(event)

        # 加载预览图像
        QTimer.singleShot(100, self._load_preview_image)

    # 特殊功能按钮实现
    def _create_template(self):
        """创建模板"""
        try:
            import numpy as np
            import cv2

            # 获取当前图像
            if not hasattr(self, 'current_test_image') or self.current_test_image is None:
                QMessageBox.warning(self, "错误", "请先加载图像")
                return

            # 检查是否有选中的ROI
            if not self.roi_regions:
                QMessageBox.warning(self, "错误", "请先绘制ROI区域作为模板")
                return

            # 使用第一个ROI作为模板区域
            roi = self.roi_regions[0]
            x, y, w, h = roi['x'], roi['y'], roi['width'], roi['height']

            # 从numpy图像中提取模板区域
            template_region = self.current_test_image[y:y+h, x:x+w].copy()

            if template_region.size == 0:
                QMessageBox.warning(self, "错误", "ROI区域无效")
                return

            # 保存模板
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存模板", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
            )
            if file_path:
                # 保存模板图像
                cv2.imwrite(file_path, template_region)

                # 更新配置
                if "parameters" not in self.config_data:
                    self.config_data["parameters"] = {}
                self.config_data["parameters"]["template_path"] = file_path

                # 显示模板预览
                self._show_template_preview(template_region)

                QMessageBox.information(self, "成功", f"模板已创建并保存: {file_path}")
                logger.info(f"模板已创建: {file_path}, 尺寸: {template_region.shape}")

        except Exception as e:
            logger.error(f"创建模板失败: {e}")
            QMessageBox.warning(self, "错误", f"创建模板失败: {str(e)}")

    def _show_template_preview(self, template_image):
        """显示模板预览"""
        try:
            import cv2
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel
            from PyQt5.QtGui import QImage

            # 创建预览对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("模板预览")
            dialog.setFixedSize(300, 300)

            layout = QVBoxLayout(dialog)

            # 转换图像为QPixmap显示
            height, width = template_image.shape[:2]
            if len(template_image.shape) == 3:
                rgb_image = cv2.cvtColor(template_image, cv2.COLOR_BGR2RGB)
                bytes_per_line = 3 * width
                q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            else:
                bytes_per_line = width
                q_image = QImage(template_image.data, width, height, bytes_per_line, QImage.Format_Grayscale8)

            pixmap = QPixmap.fromImage(q_image)

            # 缩放到合适大小
            scaled_pixmap = pixmap.scaled(250, 250, Qt.KeepAspectRatio, Qt.SmoothTransformation)

            label = QLabel()
            label.setPixmap(scaled_pixmap)
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)

            info_label = QLabel(f"模板尺寸: {width} x {height}")
            info_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(info_label)

            dialog.exec_()

        except Exception as e:
            logger.error(f"显示模板预览失败: {e}")

    def _load_template_image(self):
        """加载模板图像"""
        try:
            import cv2

            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择模板文件", "", "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
            )
            if file_path:
                # 加载模板图像
                template_image = cv2.imread(file_path)
                if template_image is not None:
                    # 更新配置
                    if "parameters" not in self.config_data:
                        self.config_data["parameters"] = {}
                    self.config_data["parameters"]["template_path"] = file_path

                    # 显示模板预览
                    self._show_template_preview(template_image)

                    QMessageBox.information(self, "成功", f"模板已加载: {file_path}")
                    logger.info(f"模板已加载: {file_path}, 尺寸: {template_image.shape}")
                else:
                    QMessageBox.warning(self, "错误", "无法加载模板图像")
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            QMessageBox.warning(self, "错误", f"加载模板失败: {str(e)}")

    def _pick_color(self):
        """选择颜色"""
        try:
            from PyQt5.QtWidgets import QColorDialog

            # 打开颜色选择对话框
            color = QColorDialog.getColor()
            if color.isValid():
                # 获取RGB值
                rgb = (color.red(), color.green(), color.blue())

                # 更新配置
                if "parameters" not in self.config_data:
                    self.config_data["parameters"] = {}
                self.config_data["parameters"]["target_color"] = rgb

                # 更新按钮显示和样式
                self.color_pick_btn.setText(f"🎨 颜色: RGB{rgb}")
                # 更新按钮背景色以显示选择的颜色
                text_color = THEME_COLORS["text_on_primary_bg"] if sum(rgb) < 384 else THEME_COLORS["text_on_warning_bg"]
                self.color_pick_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: rgb({rgb[0]}, {rgb[1]}, {rgb[2]});
                        color: {text_color};
                        border: 2px solid {THEME_COLORS["dark_border_primary"]};
                        border-radius: 6px;
                        padding: 8px 12px;
                        font-size: 13px;
                        font-weight: 500;
                    }}
                    QPushButton:hover {{
                        border-color: {THEME_COLORS["primary"]};
                        background-color: rgb({min(255, rgb[0]+20)}, {min(255, rgb[1]+20)}, {min(255, rgb[2]+20)});
                    }}
                    QPushButton:pressed {{
                        background-color: rgb({max(0, rgb[0]-20)}, {max(0, rgb[1]-20)}, {max(0, rgb[2]-20)});
                    }}
                """)

                QMessageBox.information(self, "成功", f"已选择颜色: RGB{rgb}")
                logger.info(f"已选择颜色: {rgb}")

        except Exception as e:
            logger.error(f"选择颜色失败: {e}")
            QMessageBox.warning(self, "错误", f"选择颜色失败: {str(e)}")

    def _set_color_range(self):
        """设置颜色范围"""
        try:
            from PyQt5.QtWidgets import QInputDialog

            # 获取当前颜色范围
            current_range = self.config_data.get("parameters", {}).get("color_tolerance", 30)

            # 输入颜色容差
            tolerance, ok = QInputDialog.getInt(
                self, "颜色范围", "请输入颜色容差 (0-255):",
                current_range, 0, 255
            )

            if ok:
                # 更新配置
                if "parameters" not in self.config_data:
                    self.config_data["parameters"] = {}
                self.config_data["parameters"]["color_tolerance"] = tolerance

                # 更新按钮显示和样式
                self.color_range_btn.setText(f"🌈 容差: {tolerance}")
                # 根据容差值设置按钮颜色
                intensity = min(255, tolerance * 2)  # 容差越大，颜色越亮
                bg_color = f"rgb({intensity//2}, {intensity}, {intensity//2})"
                self.color_range_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {bg_color};
                        color: {THEME_COLORS["text_primary"]};
                        border: 2px solid {THEME_COLORS["dark_border_primary"]};
                        border-radius: 6px;
                        padding: 8px 12px;
                        font-size: 13px;
                        font-weight: 500;
                    }}
                    QPushButton:hover {{
                        border-color: {THEME_COLORS["primary"]};
                        background-color: {THEME_COLORS["warning_hover"]};
                    }}
                    QPushButton:pressed {{
                        background-color: {THEME_COLORS["warning_pressed"]};
                    }}
                """)

                QMessageBox.information(self, "成功", f"颜色容差已设置为: {tolerance}")
                logger.info(f"颜色容差已设置: {tolerance}")

        except Exception as e:
            logger.error(f"设置颜色范围失败: {e}")
            QMessageBox.warning(self, "错误", f"设置颜色范围失败: {str(e)}")

    def _train_model(self):
        """训练模型"""
        try:
            # 获取当前算法名称
            algorithm_name = self.algorithm_combo.currentData()

            if 'yolo' in algorithm_name:
                QMessageBox.information(self, "训练", "YOLO模型训练功能正在开发中...")
            elif 'classification' in algorithm_name:
                QMessageBox.information(self, "训练", "图像分类模型训练功能正在开发中...")
            elif 'face_detection' in algorithm_name:
                QMessageBox.information(self, "训练", "人脸检测模型训练功能正在开发中...")
            elif 'text_detection' in algorithm_name:
                QMessageBox.information(self, "训练", "文本检测模型训练功能正在开发中...")
            else:
                QMessageBox.information(self, "训练", f"算法 {algorithm_name} 的训练功能正在开发中...")

            logger.info(f"训练请求: {algorithm_name}")

        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            QMessageBox.warning(self, "错误", f"训练模型失败: {str(e)}")

    def _preview_algorithm(self):
        """预览算法"""
        try:
            algorithm_name = self.algorithm_combo.currentData()
            if not algorithm_name:
                QMessageBox.warning(self, "错误", "请先选择算法")
                return

            # 检查是否有测试图像
            if not hasattr(self, 'current_test_image') or self.current_test_image is None:
                QMessageBox.warning(self, "错误", "请先加载测试图像")
                return

            # 根据算法类型执行不同的预览
            if 'template_matching' in algorithm_name:
                self._preview_template_matching()
            elif 'color_detection' in algorithm_name:
                self._preview_color_detection()
            elif 'edge_detection' in algorithm_name:
                self._preview_edge_detection()
            elif 'gaussian_blur' in algorithm_name:
                self._preview_gaussian_blur()
            else:
                # 通用预览
                self._preview_generic_algorithm(algorithm_name)

        except Exception as e:
            logger.error(f"预览算法失败: {e}")
            QMessageBox.warning(self, "错误", f"预览算法失败: {str(e)}")

    def _preview_template_matching(self):
        """预览模板匹配"""
        try:
            import cv2
            import numpy as np

            # 检查是否有模板
            template_path = self.config_data.get("parameters", {}).get("template_path")
            if not template_path:
                QMessageBox.warning(self, "错误", "请先创建或加载模板")
                return

            # 加载模板
            template = cv2.imread(template_path)
            if template is None:
                QMessageBox.warning(self, "错误", "无法加载模板文件")
                return

            # 获取参数
            parameters = self.config_data.get("parameters", {})
            threshold = parameters.get("threshold", 0.8)
            method = getattr(cv2, parameters.get("method", "TM_CCOEFF_NORMED"))

            # 执行模板匹配
            result_image = self.current_test_image.copy()

            # 转换为灰度图进行匹配
            gray_image = cv2.cvtColor(self.current_test_image, cv2.COLOR_BGR2GRAY)
            gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            # 执行匹配
            match_result = cv2.matchTemplate(gray_image, gray_template, method)

            # 找到匹配位置
            if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                locations = np.where(match_result <= (1.0 - threshold))
            else:
                locations = np.where(match_result >= threshold)

            # 绘制匹配结果
            template_h, template_w = gray_template.shape
            match_count = 0

            for pt in zip(*locations[::-1]):
                # 绘制匹配框
                cv2.rectangle(result_image, pt, (pt[0] + template_w, pt[1] + template_h), (0, 255, 0), 2)

                # 获取置信度
                confidence = match_result[pt[1], pt[0]]
                if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                    confidence = 1.0 - confidence

                # 绘制置信度文本
                cv2.putText(result_image, f"{confidence:.3f}", (pt[0], pt[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                match_count += 1

                # 限制显示数量
                if match_count >= 10:
                    break

            # 显示结果
            self._show_preview_result(result_image, f"模板匹配结果 - 找到 {match_count} 个匹配")

        except Exception as e:
            logger.error(f"模板匹配预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"模板匹配预览失败: {str(e)}")

    def _preview_color_detection(self):
        """预览颜色检测"""
        try:
            import cv2
            import numpy as np

            # 获取参数
            parameters = self.config_data.get("parameters", {})
            target_color = parameters.get("target_color", (0, 255, 0))  # 默认绿色
            tolerance = parameters.get("color_tolerance", 30)

            # 转换为HSV色彩空间
            hsv_image = cv2.cvtColor(self.current_test_image, cv2.COLOR_BGR2HSV)

            # 将RGB颜色转换为HSV
            target_bgr = np.uint8([[[target_color[2], target_color[1], target_color[0]]]])
            target_hsv = cv2.cvtColor(target_bgr, cv2.COLOR_BGR2HSV)[0][0]

            # 定义颜色范围
            lower_bound = np.array([max(0, target_hsv[0] - tolerance//2), 50, 50])
            upper_bound = np.array([min(179, target_hsv[0] + tolerance//2), 255, 255])

            # 创建掩码
            mask = cv2.inRange(hsv_image, lower_bound, upper_bound)

            # 创建结果图像
            result_image = self.current_test_image.copy()
            result_image[mask > 0] = target_color

            # 显示结果
            self._show_preview_result(result_image, f"颜色检测结果 - 目标颜色: RGB{target_color}")

        except Exception as e:
            logger.error(f"颜色检测预览失败: {e}")
            QMessageBox.warning(self, "预览失败", f"颜色检测预览失败: {str(e)}")

    def _show_preview_result(self, result_image, title="预览结果"):
        """显示预览结果"""
        try:
            import cv2
            import numpy as np
            from PyQt5.QtGui import QImage

            # 确保图像不为空
            if result_image is None or result_image.size == 0:
                logger.error("预览结果图像为空")
                return

            logger.debug(f"输入图像形状: {result_image.shape}, 数据类型: {result_image.dtype}")

            # 处理不同的图像格式
            if len(result_image.shape) == 2:
                # 灰度图像，转换为RGB
                rgb_image = cv2.cvtColor(result_image, cv2.COLOR_GRAY2RGB)
                logger.debug("灰度图像转换为RGB")
            elif len(result_image.shape) == 3:
                if result_image.shape[2] == 3:
                    # BGR图像，转换为RGB
                    rgb_image = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
                    logger.debug("BGR图像转换为RGB")
                elif result_image.shape[2] == 4:
                    # BGRA图像，转换为RGB
                    rgb_image = cv2.cvtColor(result_image, cv2.COLOR_BGRA2RGB)
                    logger.debug("BGRA图像转换为RGB")
                else:
                    logger.error(f"不支持的图像通道数: {result_image.shape[2]}")
                    return
            else:
                logger.error(f"不支持的图像形状: {result_image.shape}")
                return

            # 确保数据类型为uint8
            if rgb_image.dtype != np.uint8:
                # 如果是浮点数，假设范围是0-1，转换为0-255
                if rgb_image.dtype in [np.float32, np.float64]:
                    if rgb_image.max() <= 1.0:
                        rgb_image = (rgb_image * 255).astype(np.uint8)
                    else:
                        rgb_image = np.clip(rgb_image, 0, 255).astype(np.uint8)
                else:
                    rgb_image = rgb_image.astype(np.uint8)
                logger.debug(f"数据类型转换为uint8")

            height, width, channels = rgb_image.shape
            bytes_per_line = channels * width

            # 确保数据连续性
            if not rgb_image.flags['C_CONTIGUOUS']:
                rgb_image = np.ascontiguousarray(rgb_image)
                logger.debug("确保数据连续性")

            # 验证图像数据
            logger.debug(f"RGB图像: 形状={rgb_image.shape}, 数据类型={rgb_image.dtype}, "
                        f"最小值={rgb_image.min()}, 最大值={rgb_image.max()}")

            q_image = QImage(rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # 检查QImage是否有效
            if q_image.isNull():
                logger.error("创建的QImage无效")
                # 尝试创建一个简单的测试图像
                test_image = np.zeros((height, width, 3), dtype=np.uint8)
                test_image[:] = [128, 128, 128]  # 灰色
                q_image = QImage(test_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                if q_image.isNull():
                    logger.error("连测试QImage都无法创建")
                    return

            pixmap = QPixmap.fromImage(q_image)

            # 检查QPixmap是否有效
            if pixmap.isNull():
                logger.error("创建的QPixmap无效")
                return

            # 更新ROI视图
            self.roi_view.set_image(pixmap)

            # 更新当前测试图像（保持BGR格式供算法使用）
            if len(result_image.shape) == 3 and result_image.shape[2] == 3:
                self.current_test_image = result_image.copy()
            else:
                # 如果原图不是BGR格式，转换回BGR
                self.current_test_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

            logger.info(f"预览完成: {title}, 图像尺寸: {width}x{height}")

        except Exception as e:
            logger.error(f"显示预览结果失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

            # 创建一个错误提示图像
            try:
                error_image = np.zeros((480, 640, 3), dtype=np.uint8)
                error_image[:] = [64, 64, 64]  # 深灰色
                cv2.putText(error_image, "Preview Error", (200, 240),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

                q_image = QImage(error_image.data, 640, 480, 640*3, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)
                self.roi_view.set_image(pixmap)
                logger.info("显示错误提示图像")
            except:
                logger.error("无法创建错误提示图像")
