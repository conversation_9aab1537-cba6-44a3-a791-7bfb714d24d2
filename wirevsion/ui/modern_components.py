#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化UI组件库

提供一系列现代化的UI组件，统一风格和交互体验
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QScrollArea, QGroupBox,
    QLineEdit, QTextEdit, QComboBox, QSlider, QProgressBar,
    QCheckBox, QRadioButton, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QRectF, QPointF, QSize, QTimer, QPoint, pyqtProperty
from PyQt5.QtGui import QColor, QPalette, QFont, QPainter, QPainterPath, QLinearGradient, QPixmap, QBrush, QPen, QIcon, QRadialGradient
from typing import Optional, List, Dict, Any, Union, Callable
from loguru import logger

# 主题颜色 - 暗色主题
THEME_COLORS = {
    # 背景色
    "dark_bg_app": "#121212",  # 应用程序背景，更深的黑灰色
    "dark_bg_sidebar": "#1a1a1a",  # 侧边栏背景，略浅于应用背景
    "dark_bg_content": "#1e1e1e",  # 内容区背景，比侧边栏略浅
    "dark_bg_card": "#242424",  # 卡片背景，比内容区略浅
    "dark_bg_input": "#2c2c2c",  # 输入框背景，适中灰色
    "dark_bg_header_footer": "#1c1c1c",  # 标题栏、状态栏、工具栏背景
    "dark_bg_card_header": "#282828",  # 卡片头部背景
    
    # 表面色
    "dark_surface_hover": "#2d2d2d",  # 悬停表面，浅灰色
    "dark_surface_pressed": "#333333",  # 按下表面，比悬停更浅
    "dark_surface_selected": "#2a2a2a",  # 选中表面
    
    # 边框色
    "dark_border_primary": "#3a3a3a",  # 主要边框
    "dark_border_secondary": "#303030",  # 次要边框
    
    # 文本颜色
    "text_primary": "#f0f0f0",  # 主要文本，亮白色增强对比
    "text_secondary": "#b0b0b0",  # 次要文本，亮灰色
    "text_placeholder": "#888888",  # 输入框占位符文本
    "text_disabled": "#707070",  # 禁用文本
    "text_title": "#ffffff",  # 标题文本，纯白色
    "text_on_primary_bg": "#ffffff",  # 在主色背景上的文本
    "text_on_warning_bg": "#212529",  # 在警告色背景上的文本
    "text_on_dark_selected_bg": "#ffffff",  # 深色选中背景上的文本
    
    # 主色系 - 蓝色
    "primary": "#2979ff",  # 主色，更亮的蓝色
    "primary_hover": "#1565c0",  # 主色悬停
    "primary_pressed": "#0d47a1",  # 主色按下
    "primary_transparent": "#2979ff22",  # 半透明主色
    
    # 次色系
    "secondary": "#5c6bc0",  # 次色，更柔和的蓝紫色
    "secondary_hover": "#4a5aa8",  # 次色悬停
    "secondary_pressed": "#3949ab",  # 次色按下
    "success": "#43a047",  # 成功色，柔和绿色
    "success_hover": "#388e3c",  # 成功色悬停
    "success_pressed": "#2e7d32",  # 成功色按下
    "warning": "#ffb300",  # 警告色，金黄色
    "warning_hover": "#ffa000",  # 警告色悬停
    "warning_pressed": "#ff8f00",  # 警告色按下
    "danger": "#e53935",  # 危险色，鲜红色
    "danger_hover": "#d32f2f",  # 危险色悬停
    "danger_pressed": "#c62828",  # 危险色按下
    "info": "#00acc1",  # 信息色，青蓝色
}


class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title: str = "", subtitle: str = "", parent=None):
        super().__init__(parent)
        
        self.title = title
        self.subtitle = subtitle
        
        # 设置属性
        self.setObjectName("ModernCard")
        self.setMinimumSize(200, 100)
        
        # 阴影和边框
        self._shadow_color = QColor(0, 0, 0, 80)
        self._border_radius = 8
        
        # 初始化UI
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        # 设置样式
        self.setStyleSheet(f"""
            #ModernCard {{
                background-color: {THEME_COLORS["dark_bg_card"]};
                border-radius: {self._border_radius}px;
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
            }}
        """)
        
        # 主布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(12, 12, 12, 12)
        self.layout.setSpacing(8)
        
        # 标题栏
        if self.title:
            self.header_layout = QVBoxLayout()
            self.header_layout.setContentsMargins(0, 0, 0, 0)
            self.header_layout.setSpacing(4)
            
            # 标题
            self.title_label = QLabel(self.title)
            self.title_label.setStyleSheet(f"""
                QLabel {{
                    color: {THEME_COLORS["text_title"]};
                    font-size: 16px;
                    font-weight: 600;
                }}
            """)
            self.header_layout.addWidget(self.title_label)
            
            # 副标题
            if self.subtitle:
                self.subtitle_label = QLabel(self.subtitle)
                self.subtitle_label.setStyleSheet(f"""
                    QLabel {{
                        color: {THEME_COLORS["text_secondary"]};
                        font-size: 12px;
                    }}
                """)
                self.header_layout.addWidget(self.subtitle_label)
            
            # 分隔线
            self.separator = QFrame()
            self.separator.setFrameShape(QFrame.HLine)
            self.separator.setStyleSheet(f"""
                QFrame {{
                    background-color: {THEME_COLORS["dark_border_secondary"]};
                    max-height: 1px;
                    margin-top: 8px;
                    margin-bottom: 4px;
                }}
            """)
            
            self.layout.addLayout(self.header_layout)
            self.layout.addWidget(self.separator)
        
        # 内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(8)
        
        self.layout.addWidget(self.content_widget)
    
    def add_content(self, widget: QWidget):
        """添加内容组件"""
        self.content_layout.addWidget(widget)
    
    def paintEvent(self, event):
        """绘制事件 - 添加阴影效果"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制阴影
        path = QPainterPath()
        path.addRoundedRect(QRectF(3, 3, self.width()-6, self.height()-6), 
                           self._border_radius, self._border_radius)
        
        # 使用径向渐变创建阴影效果
        center = QPoint(int(self.width()/2), int(self.height()/2))
        shadow_radius = max(self.width(), self.height()) / 2
        gradient = QRadialGradient(center, shadow_radius)
        shadow_color_center = QColor(self._shadow_color)
        shadow_color_center.setAlpha(30)  # 中心透明度低
        shadow_color_edge = QColor(self._shadow_color)
        shadow_color_edge.setAlpha(5)  # 边缘透明度更低
        
        gradient.setColorAt(0, shadow_color_center)
        gradient.setColorAt(1, shadow_color_edge)
        
        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(gradient))
        painter.drawPath(path)
        
        # 绘制卡片内部微妙渐变
        content_path = QPainterPath()
        content_path.addRoundedRect(QRectF(1, 1, self.width()-2, self.height()-2), 
                                   self._border_radius, self._border_radius)
        
        card_bg = QColor(THEME_COLORS["dark_bg_card"])
        card_bg_lighter = card_bg.lighter(105)
        
        card_gradient = QLinearGradient(0, 0, 0, self.height())
        card_gradient.setColorAt(0, card_bg_lighter)
        card_gradient.setColorAt(1, card_bg)
        
        painter.setBrush(QBrush(card_gradient))
        painter.drawPath(content_path)


class ModernButton(QPushButton):
    PRIMARY = "primary"
    SECONDARY = "secondary"
    SUCCESS = "success"
    WARNING = "warning"
    DANGER = "danger"
    TEXT = "text" # 新增文本按钮类型
    
    def __init__(self, text: str, button_type: str = PRIMARY, 
                 icon: Optional[QIcon] = None, parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.icon = icon
        self.border_radius = 6 # 统一按钮圆角
        self.setCursor(Qt.PointingHandCursor)
        if self.icon:
            self.setIcon(self.icon)
            self.setIconSize(QSize(16, 16)) # 合适的图标大小
        self._setup_style()
        # self._setup_animations() # 动画可以后续添加
        logger.debug(f"现代化按钮创建: {text} ({button_type})")
    
    def _setup_style(self):
        text_color = THEME_COLORS["text_on_primary_bg"]
        if self.button_type == self.WARNING:
            text_color = THEME_COLORS["text_on_warning_bg"]
        elif self.button_type == self.TEXT:
            text_color = THEME_COLORS["primary"] # 文本按钮用主色调

        common_style = f"""
            QPushButton {{
                border: none;
                padding: 8px 16px; /* 调整内边距 */
                font-size: 13px; /* 稍小字体 */
                font-weight: 500; /* 正常字重 */
                border-radius: {self.border_radius}px;
                color: {text_color};
            }}
        """
        
        type_specific_style = ""
        if self.button_type == self.PRIMARY:
            type_specific_style = f"""
                QPushButton {{ background-color: {THEME_COLORS["primary"]}; }}
                QPushButton:hover {{ background-color: {THEME_COLORS["primary_hover"]}; }}
                QPushButton:pressed {{ background-color: {THEME_COLORS["primary_pressed"]}; }}
            """
        elif self.button_type == self.SECONDARY:
            type_specific_style = f"""
                QPushButton {{
                    background-color: {THEME_COLORS["secondary"]};
                    color: {THEME_COLORS["text_on_primary_bg"]};
                }}
                QPushButton:hover {{ background-color: {THEME_COLORS["secondary_hover"]}; }}
                QPushButton:pressed {{ background-color: {THEME_COLORS["secondary_pressed"]}; }}
            """
        elif self.button_type == self.SUCCESS:
            type_specific_style = f"""
                QPushButton {{ background-color: {THEME_COLORS["success"]}; }}
                QPushButton:hover {{ background-color: {THEME_COLORS["success_hover"]}; }}
                QPushButton:pressed {{ background-color: {THEME_COLORS["success_pressed"]}; }}
            """
        elif self.button_type == self.WARNING:
            type_specific_style = f"""
                QPushButton {{
                    background-color: {THEME_COLORS["warning"]};
                    color: {THEME_COLORS["text_on_warning_bg"]};
                }}
                QPushButton:hover {{ background-color: {THEME_COLORS["warning_hover"]}; }}
                QPushButton:pressed {{ background-color: {THEME_COLORS["warning_pressed"]}; }}
            """
        elif self.button_type == self.DANGER:
            type_specific_style = f"""
                QPushButton {{ background-color: {THEME_COLORS["danger"]}; }}
                QPushButton:hover {{ background-color: {THEME_COLORS["danger_hover"]}; }}
                QPushButton:pressed {{ background-color: {THEME_COLORS["danger_pressed"]}; }}
            """
        elif self.button_type == self.TEXT:
            type_specific_style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {THEME_COLORS["primary"]};
                    padding: 4px 8px; /* 文本按钮更小的内边距 */
                }}
                QPushButton:hover {{
                    background-color: {QColor(THEME_COLORS["primary"]).lighter(190).name(QColor.HexArgb)};
                    color: {THEME_COLORS["primary_hover"]};
                }}
                QPushButton:pressed {{
                    background-color: {QColor(THEME_COLORS["primary"]).lighter(180).name(QColor.HexArgb)};
                    color: {THEME_COLORS["primary_pressed"]};
                }}
            """
        
        self.setStyleSheet(common_style + type_specific_style)


class ModernInput(QWidget):
    """现代化输入框组件"""
    
    textChanged = pyqtSignal(str)
    returnPressed = pyqtSignal()
    
    def __init__(self, placeholder: str = "", label_text: str = "", 
                 is_password: bool = False, parent=None):
        super().__init__(parent)
        
        self.placeholder = placeholder
        self.label_text = label_text
        self.is_password = is_password
        self.border_radius = 6 # 统一圆角
        
        self._setup_ui()
        logger.debug(f"现代化输入框创建: {label_text}") # 移到 _setup_ui 之后
    
    def _setup_ui(self):
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0) # 输入框通常不需要外边距，由父布局控制
        self.layout.setSpacing(4) # 标签和输入框之间的间距
        
        if self.label_text: # 只有当label_text非空时才创建和添加标签
            self.label = QLabel(self.label_text)
            label_color = THEME_COLORS["text_secondary"]
            self.label.setStyleSheet(f"""
                QLabel {{
                    color: {label_color};
                    font-size: 12px; /* 标签字号稍小 */
                    margin-bottom: 2px; /* 减小与输入框的间距 */
                }}
            """)
            self.layout.addWidget(self.label)
        else:
            self.label = None # 确保self.label存在
        
        self.input = QLineEdit()
        self.input.setPlaceholderText(self.placeholder)
        if self.is_password:
            self.input.setEchoMode(QLineEdit.Password)
        
        self.input.textChanged.connect(self.textChanged.emit)
        self.input.returnPressed.connect(self.returnPressed.emit)
        self.layout.addWidget(self.input)
        self._setup_style()

    def _setup_style(self):
        # QWidget的背景设为透明，以便父容器控制背景
        # self.setStyleSheet(f"background-color: transparent;") 
        
        input_bg = THEME_COLORS["dark_bg_input"]
        input_text_color = THEME_COLORS["text_primary"]
        input_border = THEME_COLORS["dark_border_primary"]
        input_focus_border = THEME_COLORS["primary"]
        placeholder_color = THEME_COLORS["text_placeholder"]

        self.input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {input_bg};
                color: {input_text_color};
                border: 1px solid {input_border};
                border-radius: {self.border_radius}px;
                padding: 8px 10px; /* 调整内边距 */
                font-size: 13px;
            }}
            QLineEdit:focus {{
                border: 1px solid {input_focus_border};
                /* 可以添加轻微的背景色变化以示区分 */
                /* background-color: {QColor(input_bg).lighter(110).name()}; */ 
            }}
            QLineEdit::placeholder {{
                color: {placeholder_color};
            }}
        """)

    def text(self) -> str:
        return self.input.text()

    def setText(self, text: str):
        self.input.setText(text)


class ModernProgressBar(QProgressBar):
    """现代化进度条组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(10) # 纤细型进度条
        self._setup_style()
        logger.debug("现代化进度条创建")
    
    def _setup_style(self):
        bar_bg = THEME_COLORS["dark_bg_input"]
        chunk_bg_start = THEME_COLORS["primary"]
        chunk_bg_end = QColor(THEME_COLORS["primary"]).lighter(120).name()
        text_color_on_chunk = THEME_COLORS["text_on_primary_bg"]
        text_color_on_bg = THEME_COLORS["text_secondary"]

        self.setStyleSheet(f"""
            QProgressBar {{
                border: none; /* 无边框 */
                border-radius: {self.height() / 2}px;
                text-align: center;
                background-color: {bar_bg};
                color: {text_color_on_bg}; /* 背景上的文字颜色 */
                font-size: 9px; /* 进度条内文字小一点 */
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {chunk_bg_start}, stop:1 {chunk_bg_end});
                border-radius: {self.height() / 2}px;
                /* margin: 1px; */ /* 如果需要细边框效果 */
            }}
        """)
    
    # paintEvent 可以被移除，因为样式表已经处理了大部分需求
    # 如果需要更复杂的文本处理（例如，总是确保文本与进度块或背景对比鲜明），则保留并修改paintEvent
    # def paintEvent(self, event): ...


class ModernSwitch(QCheckBox):
    """现代化开关组件"""
    def __init__(self, text: str = "", parent=None):
        super().__init__(text, parent)
        self._indicator_height = 20 # 调整指示器大小
        self._indicator_width = 40
        self.setCursor(Qt.PointingHandCursor)
        # QCheckBox 会自动根据文本和指示器调整大小，所以不建议使用setFixedSize，除非有特定需求
        # self.setFixedSize(self._indicator_width + (len(text) * 8 if text else 0) + 24, self._indicator_height + 4)
        self._setup_style()
        logger.debug(f"现代化开关创建: {text}")
    
    def _setup_style(self):
        text_color = THEME_COLORS["text_primary"]
        indicator_bg_off = THEME_COLORS["secondary"]
        indicator_bg_on = THEME_COLORS["primary"]
        indicator_handle_color = "#FFFFFF"
        indicator_hover_off = QColor(indicator_bg_off).lighter(120).name()
        indicator_hover_on = QColor(indicator_bg_on).lighter(120).name()

        self.setStyleSheet(f"""
            QCheckBox {{
                font-size: 13px;
                color: {text_color};
                spacing: 8px; /* 指示器和文本的间距 */
                padding: 2px; /* 给控件一些呼吸空间 */
            }}
            QCheckBox::indicator {{
                width: {self._indicator_width}px;
                height: {self._indicator_height}px;
                border-radius: {self._indicator_height / 2}px;
                background-color: {indicator_bg_off};
                /* 添加手柄外观 */
                image: none; /* 清除默认图像 */
            }}
            QCheckBox::indicator:checked {{
                background-color: {indicator_bg_on};
            }}
            QCheckBox::indicator:hover {{
                background-color: {indicator_hover_off};
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: {indicator_hover_on};
            }}
            /* 使用伪元素绘制手柄 */
            QCheckBox::indicator::handle {{
                background-color: {indicator_handle_color};
                border-radius: {(self._indicator_height - 6) / 2}px; /* 手柄略小于指示器高度 */
                width: {self._indicator_height - 6}px;
                height: {self._indicator_height - 6}px;
                margin: 3px;
            }}
            QCheckBox::indicator:checked::handle {{
                /* margin-left: {self._indicator_width - self._indicator_height + 3}px; */ /* 简单的向右移动，可能需要更精确计算 */
            }}
        """)
    
    # paintEvent 将被移除，因为样式表足以定义外观
    # def paintEvent(self, event): ...


class ModernComboBox(QComboBox):
    """现代化下拉框组件"""
    
    def __init__(self, items: List[str] = None, parent=None):
        super().__init__(parent)
        
        if items:
            self.addItems(items)
        
        # 设置现代化样式
        self._setup_style()
        
        logger.debug(f"现代化下拉框创建，项目数: {len(items) if items else 0}")
    
    def _setup_style(self):
        """设置现代化样式"""
        self.setStyleSheet("""
            QComboBox {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: #ffffff;
                color: #212529;
                min-width: 120px;
            }
            QComboBox:focus {
                border: 2px solid #0d6efd;
            }
            QComboBox:hover {
                border: 2px solid #adb5bd;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #dee2e6;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #6c757d;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                selection-background-color: #e7f1ff;
                padding: 4px;
            }
        """)


class ModernTabs(QWidget):
    """现代化标签页组件"""
    
    tab_changed = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.tabs = []
        self.current_tab = 0
        
        # 设置布局
        self._setup_layout()
        
        # 设置现代化样式
        self._setup_style()
        
        logger.debug("现代化标签页组件创建")
    
    def _setup_layout(self):
        """设置布局"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 标签栏
        self.tab_bar = QWidget()
        self.tab_bar_layout = QHBoxLayout(self.tab_bar)
        self.tab_bar_layout.setContentsMargins(0, 0, 0, 0)
        self.tab_bar_layout.setSpacing(0)
        
        self.main_layout.addWidget(self.tab_bar)
        
        # 内容区域
        self.content_area = QWidget()
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        
        self.main_layout.addWidget(self.content_area)
    
    def _setup_style(self):
        """设置样式"""
        tabs_bg_color = THEME_COLORS["dark_bg_content"] # Changed from dark_bg_primary
        self.setStyleSheet(f"QWidget {{ background-color: {tabs_bg_color}; }}")
    
    def add_tab(self, widget: QWidget, title: str, icon=None):
        """添加标签页"""
        tab_button = ModernButton(title, ModernButton.TEXT) 
        tab_button.setFixedHeight(36) # 统一标签按钮高度
        
        text_secondary_color = THEME_COLORS["text_secondary"]
        text_primary_color = THEME_COLORS["text_primary"]
        dark_surface_hover_color = THEME_COLORS["dark_surface_hover"]
        primary_color = THEME_COLORS["primary"]

        tab_button.setStyleSheet(f""" 
            QPushButton {{
                border: none;
                border-bottom: 2px solid transparent; /* 未选中时透明底边框 */
                padding: 8px 12px;
                font-size: 14px;
                font-weight: 500;
                color: {text_secondary_color};
            }}
            QPushButton:hover {{
                color: {text_primary_color};
                border-bottom: 2px solid {dark_surface_hover_color};
            }}
            QPushButton:checked {{
                color: {primary_color};
                border-bottom: 2px solid {primary_color};
                font-weight: 600;
            }}
        """)
        tab_button.setCheckable(True)
        tab_button.clicked.connect(lambda checked, b=tab_button: self._switch_to_tab(self.tabs.index(next(t for t in self.tabs if t['button'] == b)) if checked else -1) )

        self.tab_bar_layout.addWidget(tab_button)
        widget.hide()
        self.content_layout.addWidget(widget)
        self.tabs.append({'button': tab_button, 'widget': widget, 'title': title})
        if len(self.tabs) == 1: self._switch_to_tab(0)
        logger.debug(f"添加标签页: {title}")

    def _switch_to_tab(self, index: int):
        if not (0 <= index < len(self.tabs)):
             # 如果是取消选中，不执行任何操作，或者根据需求取消所有选中
            return

        for i, tab_info in enumerate(self.tabs):
            is_current = (i == index)
            tab_info['widget'].setVisible(is_current)
            tab_info['button'].setChecked(is_current)
            # 按钮样式通过 :checked 伪类自动更新，无需手动设置

        self.current_tab = index
        self.tab_changed.emit(index)
        logger.debug(f"切换到标签页: {self.tabs[index]['title']}")


class ModernStatusBar(QWidget):
    """现代化状态栏组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(30) # 状态栏高度
        self._setup_layout()
        self._setup_style()
        logger.debug("现代化状态栏创建")
    
    def _setup_layout(self):
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(10, 0, 10, 0) # 调整边距
        self.layout.setSpacing(10)
        
        self.status_label = QLabel("就绪")
        self.layout.addWidget(self.status_label)
        self.layout.addStretch()
        
        self.progress_bar = ModernProgressBar()
        self.progress_bar.setFixedHeight(8) # 使进度条更纤细
        self.progress_bar.hide()
        self.progress_bar.setMaximumWidth(200) # 限制最大宽度
        self.layout.addWidget(self.progress_bar)
        
        self.memory_label = QLabel("内存: -- MB")
        self.layout.addWidget(self.memory_label)

    def _setup_style(self):
        bg_color = THEME_COLORS["dark_bg_header_footer"]
        border_color = THEME_COLORS["dark_border_primary"]
        text_color = THEME_COLORS["text_secondary"]

        self.setStyleSheet(f"""
            QWidget {{
                background-color: {bg_color};
                border-top: 1px solid {border_color};
            }}
            QLabel {{
                font-size: 12px; /* 统一状态栏字号 */
                color: {text_color};
                padding-top: 2px; /* 轻微调整垂直对齐 */
            }}
        """)
        # ModernProgressBar的样式在其自己的类中定义

    def update_status(self, message: str):
        self.status_label.setText(message)
    
    def show_progress(self, value: int = 0, maximum: int = 100):
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(value)
        self.progress_bar.show()
    
    def hide_progress(self):
        self.progress_bar.hide()
    
    def update_memory(self, memory_mb: float):
        self.memory_label.setText(f"内存: {memory_mb:.1f} MB")


class ModernDialog(QWidget):
    """现代化对话框组件"""
    
    accepted = pyqtSignal()
    rejected = pyqtSignal()
    
    def __init__(self, title: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title_text = title # 使用 title_text 避免与QWidget的title冲突
        
        # 设置窗口属性 (作为顶级窗口或子窗口)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint if not parent else Qt.Sheet)
        self.setAttribute(Qt.WA_TranslucentBackground, True) # 实现圆角和自定义背景
        if parent:
            self.setWindowModality(Qt.WindowModal) # 如果有父窗口，则模态
        else:
            self.setWindowModality(Qt.ApplicationModal) # 否则应用模态

        self._main_widget = QWidget(self) # 用于绘制圆角背景和边框的主内容Widget
        self._main_widget_layout = QVBoxLayout(self._main_widget)
        
        # 将主布局设置到 self (ModernDialog自身)
        outer_layout = QVBoxLayout(self)
        outer_layout.setContentsMargins(0,0,0,0) # 无边框对话框通常不需要外边距
        outer_layout.addWidget(self._main_widget)
        
        self._setup_layout() # 布局添加到 self._main_widget_layout
        self._setup_style()
        
        logger.debug(f"现代化对话框创建: {self.title_text}")

    def _setup_layout(self):
        self._main_widget_layout.setContentsMargins(20, 20, 20, 20) # 内边距
        self._main_widget_layout.setSpacing(15)
        
        if self.title_text:
            self.title_label = QLabel(self.title_text)
            self.title_label.setObjectName("titleLabel") # 用于样式表选择
            self._main_widget_layout.addWidget(self.title_label)
        
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self._main_widget_layout.addWidget(self.content_widget, 1) # 占据主要空间
        
        self.button_layout_widget = QWidget()
        self.button_layout = QHBoxLayout(self.button_layout_widget)
        self.button_layout.setContentsMargins(0,10,0,0) # 按钮区域顶部留些间距
        self.button_layout.setSpacing(10)
        self.button_layout.addStretch()
        
        self.cancel_button = ModernButton("取消", ModernButton.SECONDARY)
        self.cancel_button.clicked.connect(self.reject)
        self.button_layout.addWidget(self.cancel_button)
        
        self.ok_button = ModernButton("确定", ModernButton.PRIMARY)
        self.ok_button.clicked.connect(self.accept)
        self.button_layout.addWidget(self.ok_button)
        
        self._main_widget_layout.addWidget(self.button_layout_widget)

    def _setup_style(self):
        dialog_bg = THEME_COLORS["dark_bg_content"] # 对话框背景
        dialog_border = THEME_COLORS["dark_border_primary"]
        title_text_color = THEME_COLORS["text_title"]
        message_text_color = THEME_COLORS["text_primary"]
        
        # 应用到绘制背景和圆角的_main_widget
        self._main_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {dialog_bg};
                border-radius: 10px; /* 圆角 */
                border: 1px solid {dialog_border}; /* 轻微边框 */
            }}
        """)
        
        if hasattr(self, 'title_label'):
            self.title_label.setStyleSheet(f"""
                QLabel#titleLabel {{
                    color: {title_text_color};
                    font-size: 16px; /* 调整字号 */
                    font-weight: 600;
                    padding-bottom: 5px; /* 标题下方留白 */
                }}
            """)
        
        # 如果对话框内部有其他QLabel作为消息体，可以通过对象名设置样式
        # 例如，在add_content中添加的QLabel，可以设置其objectName="messageLabel"
        # self.setStyleSheet(self.styleSheet() + f"QLabel#messageLabel {{ color: {message_text_color}; font-size: 13px; }}")

    def add_content(self, widget: QWidget):
        self.content_layout.addWidget(widget)
    
    def accept(self):
        self.accepted.emit()
        self.close()
    
    def reject(self):
        self.rejected.emit()
        self.close() 