#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机和图像处理工具

提供相机管理、图像处理和显示的工具类
"""

import sys
import time
import numpy as np
import cv2
from PyQt5.QtWidgets import QApplication, QLabel, QScrollArea, QWidget, QMainWindow
from PyQt5.QtGui import QImage, QPixmap, QColor
from PyQt5.QtCore import Qt, QTimer
from loguru import logger


class UIRefresher:
    """UI刷新工具类，负责强制刷新UI组件"""
    
    @staticmethod
    def force_refresh_widget(widget):
        """强制刷新单个组件"""
        if widget:
            # 确保组件可见
            if widget.isHidden():
                if widget.objectName() in ["qt_scrollarea_vcontainer", "qt_scrollarea_hcontainer"]:
                    # 对于这些特殊的Qt内部容器，我们不强制设置可见
                    # 但记录一下这个情况用于调试
                    logger.debug(f"跳过设置 {widget.objectName()} 可见，这是Qt内部管理的控件")
                else:
                    logger.warning(f"组件 {widget.objectName() if hasattr(widget, 'objectName') else '未命名'} 不可见，设置为可见")
                    widget.setVisible(True)
                    widget.show()
            
            # 检查是否为QLabel并且有有效的pixmap
            if hasattr(widget, 'pixmap') and callable(widget.pixmap) and widget.pixmap() is not None:
                logger.debug(f"QLabel有有效的pixmap，尺寸={widget.pixmap().width()}x{widget.pixmap().height()}")
            
            # 强制重绘
            widget.update()
            widget.repaint()
            
            # 处理所有待处理的事件，确保UI响应
            QApplication.processEvents()
    
    @staticmethod
    def force_refresh_all(main_widget, *widgets):
        """强制刷新多个组件"""
        for widget in widgets:
            if widget:
                UIRefresher.force_refresh_widget(widget)
        
        # 刷新主窗口
        if main_widget:
            parent = main_widget
            # 向上查找主窗口
            while parent and not isinstance(parent, QMainWindow):
                parent = parent.parentWidget()
            
            if parent:
                logger.debug("刷新主窗口")
                parent.update()
                parent.repaint()
        
        # 最后确保处理所有事件
        QApplication.processEvents()
    
    @staticmethod
    def deep_refresh(widget, find_children=True):
        """深度刷新一个组件及其所有子组件"""
        if not widget:
            return
        
        # 首先刷新组件本身
        UIRefresher.force_refresh_widget(widget)
        
        # 然后查找并刷新所有子组件
        if find_children:
            for child in widget.findChildren(QWidget):
                # 不刷新不可见的滚动区域容器，这是Qt内部管理的
                if child.objectName() in ["qt_scrollarea_vcontainer", "qt_scrollarea_hcontainer"]:
                    continue
                    
                # 刷新每个子组件
                UIRefresher.force_refresh_widget(child)
                
        # 最后再刷新一次父组件，确保整体效果
        widget.update()
        widget.repaint()
        QApplication.processEvents()
    
    @staticmethod
    def delayed_refresh(widget):
        """延迟刷新，解决某些平台上的渲染延迟问题"""
        if widget:
            widget.update()
            widget.repaint()
            QApplication.processEvents()


class ImageDisplayManager:
    """图像显示管理器，负责图像转换、显示和刷新"""
    
    def __init__(self, image_view, image_container=None, image_scroll_area=None):
        """初始化图像显示管理器
        
        Args:
            image_view: 显示图像的QLabel对象
            image_container: 包含image_view的容器
            image_scroll_area: 滚动区域
        """
        self.image_view = image_view
        self.image_container = image_container
        self.image_scroll_area = image_scroll_area
        self.original_pixmap = None
        self.zoom_factor = 1.0
    
    def update_display(self, image):
        """更新图像显示
        
        Args:
            image: BGR或RGB格式的numpy数组
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 首先确保所有组件可见
            self._ensure_components_visible()
            
            if image is None:
                logger.error("尝试更新空图像")
                return False
            
            # 确保图像是有效的numpy数组
            if not isinstance(image, np.ndarray):
                logger.error(f"图像类型错误: {type(image)}")
                return False
                
            # 创建图像副本，避免引用原始数据导致并发问题
            image = image.copy()
            
            # 确保图像数据是连续的
            if not image.flags['C_CONTIGUOUS']:
                image = np.ascontiguousarray(image)
            
            # 确保图像是RGB格式
            if len(image.shape) == 2:  # 灰度图像
                # 转换为RGB
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            elif len(image.shape) == 3 and image.shape[2] == 3:
                # 检查是BGR还是RGB格式，并进行必要的转换
                # 使用通道平均值比较的方法检测格式
                # BGR格式的图像通常B通道值大于R通道值
                avg_b = np.mean(image[:, :, 0])
                avg_g = np.mean(image[:, :, 1])
                avg_r = np.mean(image[:, :, 2])
                
                # 如果可能是BGR格式（通过比较通道均值），则转换为RGB
                if avg_b > avg_r * 1.1 and avg_g > avg_r:  # B和G通道均值明显大于R通道
                    logger.debug(f"检测到BGR格式图像(B={avg_b:.2f}, G={avg_g:.2f}, R={avg_r:.2f})，转换为RGB")
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    # 转换后再次确保数据连续
                    image = np.ascontiguousarray(image)
            
            # 获取图像尺寸
            height, width = image.shape[:2]
            
            # 安全检查：确保图像大小合理
            if width <= 0 or height <= 0:
                logger.error(f"图像尺寸无效: {width}x{height}")
                return False
                
            if width > 10000 or height > 10000:  # 防止异常大图像
                logger.warning(f"图像尺寸过大: {width}x{height}，将调整大小")
                scale = min(10000 / width, 10000 / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height))
                width, height = new_width, new_height
            
            # 创建QImage - 必须是RGB格式
            bytes_per_line = 3 * width
            q_img = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            # 创建QPixmap
            pixmap = QPixmap.fromImage(q_img)
            self.original_pixmap = pixmap  # 保存原始pixmap用于缩放
            
            # 更新标签
            if hasattr(self, 'image_view') and self.image_view is not None:
                # 应用当前缩放因子
                if self.zoom_factor != 1.0 and not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(
                        int(width * self.zoom_factor), 
                        int(height * self.zoom_factor),
                        Qt.KeepAspectRatio, 
                        Qt.SmoothTransformation
                    )
                    self.image_view.setPixmap(scaled_pixmap)
                else:
                    self.image_view.setPixmap(pixmap)
                    
                self.image_view.setAlignment(Qt.AlignCenter)
                
                # 确保标签可见
                self.image_view.setVisible(True)
                self.image_view.show()
            
            # 更新上次更新时间
            self._last_update_time = time.time()
            
            # 记录更新
            logger.debug(f"图像显示已更新: {width}x{height}")
            return True
            
        except Exception as e:
            logger.error(f"更新图像显示时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
    def _ensure_components_visible(self):
        """确保所有UI组件可见"""
        try:
            # 确保图像视图可见
            if hasattr(self, 'image_view') and self.image_view is not None:
                self.image_view.setVisible(True)
                self.image_view.show()
            
            # 处理QApplication事件，确保可见性更改生效
            QApplication.processEvents()
            
        except Exception as e:
            logger.error(f"确保UI组件可见时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _apply_current_zoom(self, force_update=False):
        """应用当前缩放设置
        
        Args:
            force_update: 是否强制更新，即使缩放因子未变化
        """
        if self.original_pixmap is None:
            return
        
        # 获取当前图像尺寸
        h, w = self.original_pixmap.height(), self.original_pixmap.width()
        
        # 计算缩放后的尺寸
        new_w = int(w * self.zoom_factor)
        new_h = int(h * self.zoom_factor)
        
        # 如果尺寸相同且不强制更新，则不处理
        if (not force_update and
            hasattr(self, '_last_scaled_size') and
            self._last_scaled_size == (new_w, new_h)):
            return
        
        # 记录新尺寸
        self._last_scaled_size = (new_w, new_h)
        
        # 记录应用缩放信息 (降级为INFO，避免频繁输出)
        logger.info(f"应用缩放: 原始尺寸={w}x{h}, 缩放因子={self.zoom_factor}")
        
        # 根据缩放因子调整图像大小
        if self.zoom_factor != 1.0:
            scaled_pixmap = self.original_pixmap.scaled(
                new_w, new_h, 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
        else:
            # 不缩放，直接使用原图
            scaled_pixmap = self.original_pixmap
            
        # 设置到图像视图
        if self.image_view:
            # 先确保图像视图可见
            if self.image_view.isHidden():
                logger.warning("图像视图不可见，设置为可见")
                self.image_view.show()
            
            # 清除旧内容
            self.image_view.clear()
            
            # 设置新图像
            self.image_view.setPixmap(scaled_pixmap)
            self.image_view.setAlignment(Qt.AlignCenter)
            
            # 立即刷新视图
            self.image_view.update()
            self.image_view.repaint()
            QApplication.processEvents()
            
            # 深度刷新所有相关组件
            self.force_refresh()
            
            # 记录像素图大小用于调试
            # logger.debug(f"设置图像完成: 缩放后尺寸={new_w}x{new_h}")
        
        return True
    
    def set_zoom_factor(self, zoom_factor):
        """设置缩放因子
        
        Args:
            zoom_factor: 缩放因子，1.0表示100%
        """
        self.zoom_factor = zoom_factor
        return self._apply_current_zoom()
    
    def force_refresh(self):
        """强制刷新显示"""
        try:
            logger.debug("强制刷新图像显示")
            
            # 确保组件可见
            self._ensure_components_visible()
            
            # 如果有原始图像，重新应用它
            if hasattr(self, 'original_pixmap') and self.original_pixmap is not None:
                if not self.original_pixmap.isNull():
                    # 重新应用缩放
                    self._apply_current_zoom()
                    
            # 强制处理QApplication事件，确保UI更新
            QApplication.processEvents()
            
            return True
            
        except Exception as e:
            logger.error(f"强制刷新图像显示时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def _delayed_refresh(self):
        """延迟刷新，确保UI更新完成"""
        try:
            if self.image_view and self.original_pixmap and not self.original_pixmap.isNull():
                # 再次设置图像
                current_pixmap = self.image_view.pixmap()
                if current_pixmap is None or current_pixmap.isNull():
                    logger.warning("延迟刷新检测到图像丢失，重新设置")
                    self._apply_current_zoom()
                else:
                    # 确保组件可见
                    if self.image_view.isHidden():
                        logger.warning("图像视图不可见，设置为可见")
                        self.image_view.show()
                    
                    # 强制重绘
                    UIRefresher.force_refresh_widget(self.image_view)
                    
                    # 记录像素图状态
                    # logger.debug(f"延迟刷新：像素图尺寸={current_pixmap.width()}x{current_pixmap.height()}")
                
                # 如果有容器和滚动区域，也刷新它们
                if self.image_container:
                    UIRefresher.force_refresh_widget(self.image_container)
                if self.image_scroll_area:
                    UIRefresher.force_refresh_widget(self.image_scroll_area)
        except Exception as e:
            logger.error(f"延迟刷新时出错: {e}")
    
    def _final_refresh(self, force_parent=False):
        """最终刷新，用于特别慢的渲染情况
        
        Args:
            force_parent: 是否强制刷新所有父组件，包括主窗口
        """
        try:
            logger.debug("执行最终刷新")
            
            # 检查像素图是否存在
            if self.image_view and hasattr(self.image_view, 'pixmap') and callable(self.image_view.pixmap):
                current_pixmap = self.image_view.pixmap()
                if current_pixmap is None or current_pixmap.isNull():
                    logger.warning("最终刷新检测到图像仍然丢失，尝试最后一次重新设置")
                    if self.original_pixmap and not self.original_pixmap.isNull():
                        self._apply_current_zoom()
                    else:
                        logger.error("原始像素图也无效，无法恢复显示")
                else:
                    logger.debug(f"最终刷新确认：像素图正常显示，尺寸={current_pixmap.width()}x{current_pixmap.height()}")
            
            # 最后一次全局刷新
            if self.image_view:
                # 强制重绘
                self.image_view.update()
                self.image_view.repaint()
                
                # 确保所有父组件都可见
                parent = self.image_view.parent()
                while parent:
                    if parent.isHidden():
                        logger.warning(f"父组件 {parent.objectName() if hasattr(parent, 'objectName') else '未命名'} 不可见，设置为可见")
                        parent.show()
                    parent = parent.parent()
                
                # 刷新整个窗口 - 但避免深度刷新所有子组件
                main_window = self.image_view.window()
                if main_window:
                    logger.debug("刷新主窗口")
                    main_window.update()
                    main_window.repaint()
                    
                    # 如果强制刷新父组件，只刷新关键组件
                    if force_parent:
                        # 只刷新主要的UI元素而不是所有子组件
                        for widget_name in ["centralwidget", "mainToolBar", "statusbar"]:
                            widget = main_window.findChild(QWidget, widget_name)
                            if widget and widget.isVisible():
                                logger.debug(f"刷新主要UI组件: {widget_name}")
                                widget.update()
                                widget.repaint()
            
            # 确保滚动区域可见内容正确
            if self.image_scroll_area and self.image_view:
                # 确保滚动区域显示正确的位置
                self.image_scroll_area.ensureWidgetVisible(self.image_view)
            
            # 最后处理事件队列
            QApplication.processEvents()
        except Exception as e:
            logger.error(f"最终刷新时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def clear_display(self):
        """清空显示"""
        try:
            logger.debug("清空图像显示")
            
            # 清空图像视图
            if hasattr(self, 'image_view') and self.image_view is not None:
                self.image_view.clear()
                
            # 重置原始图像
            self.original_pixmap = None
            
            # 强制处理QApplication事件，确保UI更新
            QApplication.processEvents()
            
            return True
            
        except Exception as e:
            logger.error(f"清空图像显示时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False


class CameraManager:
    """相机管理器，负责相机设备的初始化、帧获取和错误处理"""
    
    def __init__(self):
        """初始化相机管理器"""
        self.camera_device = None
        # 添加帧缓存相关属性
        self.frame_cache = None
        self.last_frame_time = 0
        self.cache_validity_period = 0.1  # 缓存有效期（秒）- 降低到0.1秒确保更频繁刷新
    
    def init_camera(self):
        """初始化相机设备
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 如果相机设备已存在，先检查是否正常
            if self.camera_device is not None:
                if self.camera_device.isOpened():
                    logger.info("相机设备已经初始化并打开")
                    
                    # 捕获一帧测试相机是否真正工作
                    ret, test_frame = self.camera_device.read()
                    if ret and test_frame is not None and test_frame.size > 0:
                        logger.info("相机设备正常工作")
                        return True
                    else:
                        logger.warning("相机设备已打开但无法读取帧，尝试重新初始化")
                        try:
                            self.camera_device.release()
                        except:
                            pass  # 忽略释放错误
                else:
                    # 关闭异常的相机设备
                    logger.warning("相机设备存在但未打开，尝试重新初始化")
                    try:
                        self.camera_device.release()
                    except:
                        pass  # 忽略释放错误
            
            # 尝试捕获所有可能存在的相机设备
            available_cameras = []
            
            # 在macOS和Linux上，尝试查找可用设备
            if sys.platform == 'darwin' or sys.platform.startswith('linux'):
                import glob
                # 查找 /dev/video* 设备
                if sys.platform.startswith('linux'):
                    devices = glob.glob('/dev/video*')
                    for device in devices:
                        try:
                            index = int(device.split('video')[-1])
                            available_cameras.append(index)
                        except:
                            continue
                
                # 在macOS上尝试标准索引
                if sys.platform == 'darwin' or not available_cameras:
                    available_cameras = list(range(5))  # 尝试前5个索引
            else:
                # 在Windows上直接尝试前5个索引
                available_cameras = list(range(5))
            
            logger.info(f"尝试的相机设备索引: {available_cameras}")
            
            # 尝试打开所有可能的相机
            for index in available_cameras:
                logger.info(f"尝试打开相机设备 {index}")
                try:
                    # 使用MJPG格式可能提高性能
                    cap = cv2.VideoCapture(index)
                    
                    # 在某些系统上，需要等待一段时间让相机初始化
                    time.sleep(0.2)
                    
                    if cap.isOpened():
                        # 设置分辨率和帧率
                        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                        cap.set(cv2.CAP_PROP_FPS, 30)
                        
                        # 为了确保设置生效，读取并丢弃几帧
                        for _ in range(3):
                            cap.read()
                            time.sleep(0.1)
                        
                        # 检查是否成功设置
                        actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                        actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                        actual_fps = cap.get(cv2.CAP_PROP_FPS)
                        
                        # 读取一帧验证相机是否真正工作
                        ret, test_frame = cap.read()
                        if not ret or test_frame is None or test_frame.size == 0:
                            logger.warning(f"相机 {index} 打开但无法读取帧，尝试下一个")
                            cap.release()
                            continue
                        
                        logger.info(f"成功打开相机 {index}: 分辨率={actual_width}x{actual_height}, FPS={actual_fps}")
                        
                        # 保存相机设备引用
                        self.camera_device = cap
                        return True
                    else:
                        cap.release()
                except Exception as e:
                    logger.warning(f"尝试打开相机 {index} 出错: {e}")
                    continue
            
            # 如果无法打开任何相机，使用默认值
            logger.warning("无法打开任何相机设备，尝试使用默认值 (相机0)")
            try:
                self.camera_device = cv2.VideoCapture(0)
                time.sleep(0.3)  # 等待相机初始化
                
                if not self.camera_device.isOpened():
                    logger.error("无法打开默认相机")
                    return False
                else:
                    # 读取一帧验证
                    ret, test_frame = self.camera_device.read()
                    if ret and test_frame is not None:
                        logger.info("成功打开默认相机设备")
                        return True
                    else:
                        logger.error("默认相机打开但无法读取帧")
                        return False
            except Exception as e:
                logger.error(f"打开默认相机出错: {e}")
                return False
            
        except Exception as e:
            logger.error(f"初始化相机设备时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def get_frame(self):
        """获取一帧图像
        
        Returns:
            numpy数组: BGR格式的图像帧，或None表示失败
        """
        try:
            # 检查缓存是否有效
            current_time = time.time()
            if (self.frame_cache is not None and 
                current_time - self.last_frame_time < self.cache_validity_period):
                logger.debug(f"使用帧缓存，缓存时间={current_time - self.last_frame_time:.2f}秒")
                return self.frame_cache.copy()  # 返回缓存帧的副本
            
            # 确保相机设备已初始化
            if self.camera_device is None or not self.camera_device.isOpened():
                if not self.init_camera():
                    return self.create_error_frame("相机设备未初始化")
            
            # 尝试获取多帧，确保得到有效图像
            frame = None
            # 限制尝试次数避免无限循环
            for i in range(2):  # 减少尝试次数，只尝试2次
                # 尝试从相机读取帧
                ret, capture = self.camera_device.read()
                
                if ret and capture is not None and capture.size > 0:
                    import numpy as np
                    avg_value = np.mean(capture)
                    logger.info(f"相机读取尝试 {i+1}: 形状={capture.shape}, 平均值={avg_value:.2f}")
                    
                    # 检查帧质量
                    if avg_value > 15.0:  # 不是全黑或太暗
                        frame = capture.copy()  # 使用副本避免引用问题
                        break
                    else:
                        logger.warning(f"获取的帧太暗 (平均值={avg_value:.2f})，尝试重新获取")
                else:
                    logger.warning(f"相机读取失败，尝试 {i+1}/2")
                
                # 短暂等待，但不要太久
                time.sleep(0.05)
            
            # 如果成功获取到了有效帧
            if frame is not None and frame.size > 0:
                import numpy as np
                if np.mean(frame) > 15.0:
                    # 更新缓存
                    self.frame_cache = frame.copy()
                    self.last_frame_time = current_time
                    logger.debug("更新帧缓存")
                    return frame
                else:
                    logger.warning("获取的帧太暗，使用错误提示帧")
                    return self.create_error_frame("相机画面太暗")
            else:
                logger.warning("未能获取有效相机帧，创建错误提示帧")
                return self.create_error_frame("未能获取有效相机画面")
                
        except Exception as e:
            logger.error(f"获取相机帧出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return self.create_error_frame(f"相机错误: {str(e)}")
    
    def create_error_frame(self, message):
        """创建错误提示帧
        
        Args:
            message: 错误信息
            
        Returns:
            numpy数组，BGR格式的图像帧
        """
        try:
            # 创建黑色背景图像
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 添加红色边框
            cv2.rectangle(frame, (20, 20), (620, 460), (0, 0, 255), 3)
            
            # 添加错误信息
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(frame, message, (50, 220), font, 1, (0, 255, 255), 2)
            cv2.putText(frame, "请检查相机连接和权限", (50, 270), font, 1, (0, 255, 255), 2)
            
            # 添加时间戳
            from PyQt5.QtCore import QDateTime
            current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
            cv2.putText(frame, current_time, (50, 420), font, 0.6, (255, 255, 255), 1)
            
            return frame
        except Exception as e:
            logger.error(f"创建错误提示帧出错: {e}")
            # 创建极简错误帧，避免再次出错
            return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def release(self):
        """释放相机资源"""
        if self.camera_device is not None:
            try:
                self.camera_device.release()
                logger.info("相机资源已释放")
            except Exception as e:
                logger.error(f"释放相机资源时出错: {e}")
            finally:
                self.camera_device = None
                # 清除缓存
                self.frame_cache = None


class ImageProcessor:
    """图像处理器，负责各种图像处理算法"""
    
    @staticmethod
    def apply_gaussian_blur(image, kernel_size=5, sigma=1.5):
        """应用高斯模糊
        
        Args:
            image: 输入图像（RGB格式）
            kernel_size: 高斯核大小
            sigma: 高斯核标准差
            
        Returns:
            处理后的图像（RGB格式）
        """
        try:
            # 直接应用高斯模糊，不改变颜色空间
            return cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
        except Exception as e:
            logger.error(f"应用高斯模糊时出错: {e}")
            return image
    
    @staticmethod
    def apply_canny_edge(image, threshold1=100, threshold2=200):
        """应用Canny边缘检测
        
        Args:
            image: 输入图像（RGB格式）
            threshold1: 低阈值
            threshold2: 高阈值
            
        Returns:
            处理后的RGB格式边缘图像
        """
        try:
            # 转为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            # 应用Canny边缘检测
            edges = cv2.Canny(gray, threshold1, threshold2)
            # 转换回RGB以便显示
            return cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        except Exception as e:
            logger.error(f"应用Canny边缘检测时出错: {e}")
            return image
    
    @staticmethod
    def apply_sobel_edge(image):
        """应用Sobel边缘检测
        
        Args:
            image: 输入图像（RGB格式）
            
        Returns:
            处理后的RGB格式边缘图像
        """
        try:
            # 转为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 应用Sobel算子
            sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            
            # 计算梯度幅值
            sobel = np.sqrt(sobelx**2 + sobely**2)
            sobel = np.uint8(sobel * 255 / np.max(sobel))
            
            # 转换回RGB以便显示
            return cv2.cvtColor(sobel, cv2.COLOR_GRAY2RGB)
        except Exception as e:
            logger.error(f"应用Sobel边缘检测时出错: {e}")
            return image
    
    @staticmethod
    def apply_laplacian_edge(image):
        """应用Laplacian边缘检测
        
        Args:
            image: 输入图像（RGB格式）
            
        Returns:
            处理后的RGB格式边缘图像
        """
        try:
            # 转为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 应用Laplacian算子
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            
            # 转换为8位无符号整型
            laplacian = np.uint8(np.absolute(laplacian))
            
            # 转换回RGB以便显示
            return cv2.cvtColor(laplacian, cv2.COLOR_GRAY2RGB)
        except Exception as e:
            logger.error(f"应用Laplacian边缘检测时出错: {e}")
            return image
    
    @staticmethod
    def apply_contour_detection(image):
        """应用轮廓检测
        
        Args:
            image: 输入图像（RGB格式）
            
        Returns:
            处理后的轮廓图像（RGB格式）
        """
        try:
            # 转为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # 二值化
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 创建轮廓图像
            contour_image = np.zeros_like(image)
            cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)
            
            return contour_image
        except Exception as e:
            logger.error(f"应用轮廓检测时出错: {e}")
            return image
    
    @staticmethod
    def enhance_image(image, alpha=1.2, beta=10):
        """增强图像质量
        
        Args:
            image: 输入图像（BGR格式）
            alpha: 对比度调整参数
            beta: 亮度调整参数
            
        Returns:
            增强后的图像（BGR格式）
        """
        try:
            # 应用图像增强 - 不改变色彩空间
            return cv2.convertScaleAbs(image, alpha=alpha, beta=beta)
        except Exception as e:
            logger.error(f"增强图像时出错: {e}")
            return image
    
    @staticmethod
    def add_timestamp(image, text):
        """在图像上添加时间戳
        
        Args:
            image: 输入图像（BGR格式）
            text: 要添加的文本
            
        Returns:
            添加时间戳后的图像（BGR格式）
        """
        try:
            # 创建图像副本
            result = image.copy()
            
            # 获取图像尺寸
            h, w = result.shape[:2]
            
            # 创建半透明覆盖层
            overlay = result.copy()
            cv2.rectangle(overlay, (0, h-40), (w, h), (0, 0, 0), -1)
            cv2.addWeighted(result, 0.7, overlay, 0.3, 0, result)
            
            # 添加文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(result, text, (10, h-15), font, 0.6, (0, 255, 0), 2)
            
            # 添加当前时间
            from PyQt5.QtCore import QDateTime
            current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
            cv2.putText(result, current_time, (w-200, h-15), font, 0.6, (0, 255, 0), 2)
            
            return result
        except Exception as e:
            logger.error(f"添加时间戳时出错: {e}")
            return image


class ColorHelper:
    """颜色辅助工具类，提供颜色处理功能"""
    
    @staticmethod
    def lighten_color(color_hex, percent):
        """将颜色变亮一定百分比
        
        Args:
            color_hex: 十六进制颜色值
            percent: 变亮的百分比
            
        Returns:
            变亮后的颜色值
        """
        color = QColor(color_hex)
        h, s, l, a = color.getHslF()
        l = min(1.0, l + percent / 100.0)
        color.setHslF(h, s, l, a)
        return color.name()
    
    @staticmethod
    def darken_color(color_hex, percent):
        """将颜色变暗一定百分比
        
        Args:
            color_hex: 十六进制颜色值
            percent: 变暗的百分比
            
        Returns:
            变暗后的颜色值
        """
        color = QColor(color_hex)
        h, s, l, a = color.getHslF()
        l = max(0.0, l - percent / 100.0)
        color.setHslF(h, s, l, a)
        return color.name() 