"""
算法专用配置界面组件

为每个算法提供定制化的配置界面，支持：
- 算法特定的参数配置
- 上级结果输入选择
- 实时参数验证
- 预览功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QPushButton, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QLineEdit, QGroupBox, QSlider, QTextEdit, QTabWidget,
    QListWidget, QListWidgetItem, QSplitter, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor
from typing import Dict, Any, List, Optional
from loguru import logger
import cv2
import numpy as np

from wirevsion.ui.modern_components import ModernButton, THEME_COLORS


class BaseAlgorithmConfigWidget(QWidget):
    """算法配置基类"""

    parameter_changed = pyqtSignal(str, object)  # 参数名, 参数值
    preview_requested = pyqtSignal()

    def __init__(self, algorithm_name: str, parent=None):
        super().__init__(parent)
        self.algorithm_name = algorithm_name
        self.parameters = {}
        self.input_sources = []  # 可用的输入源
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置UI - 子类重写"""
        pass

    def setup_connections(self):
        """设置信号连接 - 子类重写"""
        pass

    def set_input_sources(self, sources: List[Dict[str, Any]]):
        """设置可用的输入源"""
        self.input_sources = sources
        self.update_input_selector()

    def update_input_selector(self):
        """更新输入选择器 - 子类重写"""
        pass

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        return self.parameters.copy()

    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        self.parameters.update(parameters)
        self.update_ui_from_parameters()

    def update_ui_from_parameters(self):
        """从参数更新UI - 子类重写"""
        pass

    def validate_parameters(self) -> bool:
        """验证参数 - 子类重写"""
        return True


class CameraSourceConfigWidget(BaseAlgorithmConfigWidget):
    """相机源配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("📷 相机配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 设备选择区域
        device_section = self._create_device_section()
        config_layout.addWidget(device_section)

        # 分辨率配置区域
        resolution_section = self._create_resolution_section()
        config_layout.addWidget(resolution_section)

        # 高级设置区域
        advanced_section = self._create_advanced_section()
        config_layout.addWidget(advanced_section)

        layout.addWidget(config_widget)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        preview_btn = ModernButton("🎥 预览相机", ModernButton.PRIMARY)
        preview_btn.setMinimumHeight(45)
        preview_btn.clicked.connect(self.preview_requested.emit)

        refresh_btn = ModernButton("🔄 刷新设备", ModernButton.SECONDARY)
        refresh_btn.setMinimumHeight(45)
        refresh_btn.clicked.connect(self._refresh_devices)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(preview_btn)

        layout.addLayout(button_layout)
        layout.addStretch()

    def _create_device_section(self):
        """创建设备选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📱 设备选择")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 相机索引选择
        device_layout = QHBoxLayout()
        device_layout.setSpacing(15)

        device_label = QLabel("相机索引:")
        device_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.camera_index = QSpinBox()
        self.camera_index.setRange(0, 10)
        self.camera_index.setValue(0)
        self.camera_index.setMinimumWidth(100)
        self.camera_index.setStyleSheet(self._get_modern_input_style())

        device_layout.addWidget(device_label)
        device_layout.addWidget(self.camera_index)
        device_layout.addStretch()

        section_layout.addLayout(device_layout)
        return section

    def _create_resolution_section(self):
        """创建分辨率配置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📐 分辨率设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 分辨率控件
        resolution_layout = QHBoxLayout()
        resolution_layout.setSpacing(15)

        # 宽度
        width_label = QLabel("宽度:")
        width_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.width_spin = QSpinBox()
        self.width_spin.setRange(320, 4096)
        self.width_spin.setValue(640)
        self.width_spin.setMinimumWidth(100)
        self.width_spin.setStyleSheet(self._get_modern_input_style())

        # 高度
        height_label = QLabel("高度:")
        height_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.height_spin = QSpinBox()
        self.height_spin.setRange(240, 3072)
        self.height_spin.setValue(480)
        self.height_spin.setMinimumWidth(100)
        self.height_spin.setStyleSheet(self._get_modern_input_style())

        resolution_layout.addWidget(width_label)
        resolution_layout.addWidget(self.width_spin)
        resolution_layout.addWidget(height_label)
        resolution_layout.addWidget(self.height_spin)
        resolution_layout.addStretch()

        section_layout.addLayout(resolution_layout)
        return section

    def _create_advanced_section(self):
        """创建高级设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("⚙️ 高级设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 帧率设置
        fps_layout = QHBoxLayout()
        fps_layout.setSpacing(15)

        fps_label = QLabel("帧率:")
        fps_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 120)
        self.fps_spin.setValue(30)
        self.fps_spin.setMinimumWidth(100)
        self.fps_spin.setStyleSheet(self._get_modern_input_style())

        fps_unit = QLabel("FPS")
        fps_unit.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; font-size: 14px;")

        fps_layout.addWidget(fps_label)
        fps_layout.addWidget(self.fps_spin)
        fps_layout.addWidget(fps_unit)
        fps_layout.addStretch()

        section_layout.addLayout(fps_layout)

        # 自动曝光
        self.auto_exposure = QCheckBox("🔆 启用自动曝光")
        self.auto_exposure.setChecked(True)
        self.auto_exposure.setStyleSheet(f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                padding: 8px;
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["primary"]};
            }}
        """)
        section_layout.addWidget(self.auto_exposure)

        return section

    def _get_modern_input_style(self):
        """获取现代化输入框样式"""
        return f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QSpinBox:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QSpinBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _refresh_devices(self):
        """刷新相机设备列表"""
        try:
            import cv2
            # 检测可用的相机设备
            available_cameras = []
            for i in range(10):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    available_cameras.append(i)
                    cap.release()

            if available_cameras:
                QMessageBox.information(self, "设备检测", f"检测到相机设备: {', '.join(map(str, available_cameras))}")
                # 设置第一个可用设备
                self.camera_index.setValue(available_cameras[0])
            else:
                QMessageBox.warning(self, "设备检测", "未检测到可用的相机设备")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"设备检测失败: {str(e)}")

    def setup_connections(self):
        self.camera_index.valueChanged.connect(lambda v: self._emit_parameter_changed("camera_index", v))
        self.width_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("width", v))
        self.height_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("height", v))
        self.fps_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("fps", v))
        self.auto_exposure.toggled.connect(lambda v: self._emit_parameter_changed("auto_exposure", v))

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class EdgeDetectionConfigWidget(BaseAlgorithmConfigWidget):
    """边缘检测配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # Canny参数设置
        canny_group = QGroupBox("Canny边缘检测参数")
        canny_layout = QFormLayout(canny_group)

        # 低阈值
        self.low_threshold = QSpinBox()
        self.low_threshold.setRange(0, 255)
        self.low_threshold.setValue(50)
        canny_layout.addRow("低阈值:", self.low_threshold)

        # 高阈值
        self.high_threshold = QSpinBox()
        self.high_threshold.setRange(0, 255)
        self.high_threshold.setValue(150)
        canny_layout.addRow("高阈值:", self.high_threshold)

        # 核大小
        self.aperture_size = QComboBox()
        self.aperture_size.addItems(["3", "5", "7"])
        self.aperture_size.setCurrentText("3")
        canny_layout.addRow("核大小:", self.aperture_size)

        # L2梯度
        self.l2_gradient = QCheckBox("使用L2梯度")
        canny_layout.addRow(self.l2_gradient)

        layout.addWidget(canny_group)

        # 预处理选项
        preprocess_group = QGroupBox("预处理")
        preprocess_layout = QFormLayout(preprocess_group)

        self.gaussian_blur = QCheckBox("高斯模糊")
        self.gaussian_blur.setChecked(True)
        preprocess_layout.addRow(self.gaussian_blur)

        self.blur_kernel = QSpinBox()
        self.blur_kernel.setRange(1, 15)
        self.blur_kernel.setValue(5)
        self.blur_kernel.setSingleStep(2)
        preprocess_layout.addRow("模糊核大小:", self.blur_kernel)

        layout.addWidget(preprocess_group)

        # 预览按钮
        preview_btn = ModernButton("预览效果", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.low_threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("low_threshold", v))
        self.high_threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("high_threshold", v))
        self.aperture_size.currentTextChanged.connect(lambda v: self._emit_parameter_changed("aperture_size", int(v)))
        self.l2_gradient.toggled.connect(lambda v: self._emit_parameter_changed("l2_gradient", v))
        self.gaussian_blur.toggled.connect(lambda v: self._emit_parameter_changed("gaussian_blur", v))
        self.blur_kernel.valueChanged.connect(lambda v: self._emit_parameter_changed("blur_kernel", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class GaussianBlurConfigWidget(BaseAlgorithmConfigWidget):
    """高斯模糊配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 模糊参数
        blur_group = QGroupBox("高斯模糊参数")
        blur_layout = QFormLayout(blur_group)

        # 核大小X
        self.kernel_x = QSpinBox()
        self.kernel_x.setRange(1, 31)
        self.kernel_x.setValue(15)
        self.kernel_x.setSingleStep(2)
        blur_layout.addRow("核大小X:", self.kernel_x)

        # 核大小Y
        self.kernel_y = QSpinBox()
        self.kernel_y.setRange(1, 31)
        self.kernel_y.setValue(15)
        self.kernel_y.setSingleStep(2)
        blur_layout.addRow("核大小Y:", self.kernel_y)

        # Sigma X
        self.sigma_x = QDoubleSpinBox()
        self.sigma_x.setRange(0.0, 10.0)
        self.sigma_x.setValue(0.0)
        self.sigma_x.setDecimals(2)
        self.sigma_x.setSingleStep(0.1)
        blur_layout.addRow("Sigma X:", self.sigma_x)

        # Sigma Y
        self.sigma_y = QDoubleSpinBox()
        self.sigma_y.setRange(0.0, 10.0)
        self.sigma_y.setValue(0.0)
        self.sigma_y.setDecimals(2)
        self.sigma_y.setSingleStep(0.1)
        blur_layout.addRow("Sigma Y:", self.sigma_y)

        layout.addWidget(blur_group)

        # 预览按钮
        preview_btn = ModernButton("预览效果", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.kernel_x.valueChanged.connect(lambda v: self._emit_parameter_changed("kernel_size_x", v))
        self.kernel_y.valueChanged.connect(lambda v: self._emit_parameter_changed("kernel_size_y", v))
        self.sigma_x.valueChanged.connect(lambda v: self._emit_parameter_changed("sigma_x", v))
        self.sigma_y.valueChanged.connect(lambda v: self._emit_parameter_changed("sigma_y", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class TemplateMatchingConfigWidget(BaseAlgorithmConfigWidget):
    """模板匹配配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 模板设置
        template_group = QGroupBox("模板设置")
        template_layout = QFormLayout(template_group)

        # 模板文件选择
        template_file_layout = QHBoxLayout()
        self.template_path = QLineEdit()
        self.template_path.setPlaceholderText("选择模板图像...")
        template_browse_btn = ModernButton("浏览", ModernButton.SECONDARY)
        template_file_layout.addWidget(self.template_path)
        template_file_layout.addWidget(template_browse_btn)
        template_layout.addRow("模板文件:", template_file_layout)

        # 匹配方法
        self.match_method = QComboBox()
        self.match_method.addItems([
            "TM_CCOEFF", "TM_CCOEFF_NORMED",
            "TM_CCORR", "TM_CCORR_NORMED",
            "TM_SQDIFF", "TM_SQDIFF_NORMED"
        ])
        self.match_method.setCurrentText("TM_CCOEFF_NORMED")
        template_layout.addRow("匹配方法:", self.match_method)

        # 匹配阈值
        self.threshold = QDoubleSpinBox()
        self.threshold.setRange(0.0, 1.0)
        self.threshold.setValue(0.8)
        self.threshold.setDecimals(3)
        self.threshold.setSingleStep(0.01)
        template_layout.addRow("匹配阈值:", self.threshold)

        # 最大匹配数
        self.max_matches = QSpinBox()
        self.max_matches.setRange(1, 100)
        self.max_matches.setValue(10)
        template_layout.addRow("最大匹配数:", self.max_matches)

        layout.addWidget(template_group)

        # 多尺度匹配
        scale_group = QGroupBox("多尺度匹配")
        scale_layout = QFormLayout(scale_group)

        self.enable_multi_scale = QCheckBox("启用多尺度匹配")
        scale_layout.addRow(self.enable_multi_scale)

        # 尺度范围
        scale_range_layout = QHBoxLayout()
        self.scale_min = QDoubleSpinBox()
        self.scale_min.setRange(0.1, 2.0)
        self.scale_min.setValue(0.5)
        self.scale_min.setDecimals(2)
        self.scale_max = QDoubleSpinBox()
        self.scale_max.setRange(0.1, 5.0)
        self.scale_max.setValue(2.0)
        self.scale_max.setDecimals(2)

        scale_range_layout.addWidget(QLabel("最小:"))
        scale_range_layout.addWidget(self.scale_min)
        scale_range_layout.addWidget(QLabel("最大:"))
        scale_range_layout.addWidget(self.scale_max)
        scale_layout.addRow("尺度范围:", scale_range_layout)

        # 尺度步长
        self.scale_step = QDoubleSpinBox()
        self.scale_step.setRange(0.01, 0.5)
        self.scale_step.setValue(0.1)
        self.scale_step.setDecimals(3)
        scale_layout.addRow("尺度步长:", self.scale_step)

        layout.addWidget(scale_group)

        # 预览按钮
        preview_btn = ModernButton("预览匹配", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.template_path.textChanged.connect(lambda v: self._emit_parameter_changed("template_path", v))
        self.match_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("method", v))
        self.threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("threshold", v))
        self.max_matches.valueChanged.connect(lambda v: self._emit_parameter_changed("max_matches", v))
        self.enable_multi_scale.toggled.connect(lambda v: self._emit_parameter_changed("enable_multi_scale", v))
        self.scale_min.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_min", v))
        self.scale_max.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_max", v))
        self.scale_step.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_step", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class ContourDetectionConfigWidget(BaseAlgorithmConfigWidget):
    """轮廓检测配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 预处理参数
        preprocess_group = QGroupBox("预处理")
        preprocess_layout = QFormLayout(preprocess_group)

        # 阈值方法
        self.threshold_method = QComboBox()
        self.threshold_method.addItems([
            "binary", "binary_inv", "adaptive_mean", "adaptive_gaussian"
        ])
        preprocess_layout.addRow("阈值方法:", self.threshold_method)

        # 阈值
        self.threshold_value = QSpinBox()
        self.threshold_value.setRange(0, 255)
        self.threshold_value.setValue(127)
        preprocess_layout.addRow("阈值:", self.threshold_value)

        # 最大值
        self.max_value = QSpinBox()
        self.max_value.setRange(0, 255)
        self.max_value.setValue(255)
        preprocess_layout.addRow("最大值:", self.max_value)

        layout.addWidget(preprocess_group)

        # 轮廓检测参数
        contour_group = QGroupBox("轮廓检测")
        contour_layout = QFormLayout(contour_group)

        # 检索模式
        self.retrieval_mode = QComboBox()
        self.retrieval_mode.addItems(["external", "tree", "ccomp", "list"])
        self.retrieval_mode.setCurrentText("external")
        contour_layout.addRow("检索模式:", self.retrieval_mode)

        # 近似方法
        self.approximation_method = QComboBox()
        self.approximation_method.addItems(["simple", "tc89_l1", "tc89_kcos"])
        self.approximation_method.setCurrentText("simple")
        contour_layout.addRow("近似方法:", self.approximation_method)

        layout.addWidget(contour_group)

        # 过滤参数
        filter_group = QGroupBox("轮廓过滤")
        filter_layout = QFormLayout(filter_group)

        # 面积过滤
        self.filter_by_area = QCheckBox("按面积过滤")
        self.filter_by_area.setChecked(True)
        filter_layout.addRow(self.filter_by_area)

        area_range_layout = QHBoxLayout()
        self.min_area = QSpinBox()
        self.min_area.setRange(0, 100000)
        self.min_area.setValue(100)
        self.max_area = QSpinBox()
        self.max_area.setRange(0, 1000000)
        self.max_area.setValue(100000)

        area_range_layout.addWidget(QLabel("最小:"))
        area_range_layout.addWidget(self.min_area)
        area_range_layout.addWidget(QLabel("最大:"))
        area_range_layout.addWidget(self.max_area)
        filter_layout.addRow("面积范围:", area_range_layout)

        # 周长过滤
        self.filter_by_perimeter = QCheckBox("按周长过滤")
        filter_layout.addRow(self.filter_by_perimeter)

        self.min_perimeter = QSpinBox()
        self.min_perimeter.setRange(0, 10000)
        self.min_perimeter.setValue(0)
        filter_layout.addRow("最小周长:", self.min_perimeter)

        layout.addWidget(filter_group)

        # 预览按钮
        preview_btn = ModernButton("预览轮廓", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.threshold_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("threshold_method", v))
        self.threshold_value.valueChanged.connect(lambda v: self._emit_parameter_changed("threshold_value", v))
        self.max_value.valueChanged.connect(lambda v: self._emit_parameter_changed("max_value", v))
        self.retrieval_mode.currentTextChanged.connect(lambda v: self._emit_parameter_changed("retrieval_mode", v))
        self.approximation_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("approximation_method", v))
        self.filter_by_area.toggled.connect(lambda v: self._emit_parameter_changed("filter_by_area", v))
        self.min_area.valueChanged.connect(lambda v: self._emit_parameter_changed("min_area", v))
        self.max_area.valueChanged.connect(lambda v: self._emit_parameter_changed("max_area", v))
        self.filter_by_perimeter.toggled.connect(lambda v: self._emit_parameter_changed("filter_by_perimeter", v))
        self.min_perimeter.valueChanged.connect(lambda v: self._emit_parameter_changed("min_perimeter", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class AlgorithmConfigWidgetFactory:
    """算法配置界面工厂类"""

    # 算法名称到配置界面类的映射
    WIDGET_MAPPING = {
        # 图像源 (4种)
        "camera": CameraSourceConfigWidget,
        "image_source.camera": CameraSourceConfigWidget,
        "file": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.file": CameraSourceConfigWidget,
        "network": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.network": CameraSourceConfigWidget,
        "video": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.video": CameraSourceConfigWidget,

        # 图像处理 (10种)
        "gaussian_blur": GaussianBlurConfigWidget,
        "image_processing.gaussian_blur": GaussianBlurConfigWidget,
        "edge_detection": EdgeDetectionConfigWidget,
        "image_processing.edge_detection": EdgeDetectionConfigWidget,
        "median_blur": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.median_blur": GaussianBlurConfigWidget,
        "bilateral_filter": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.bilateral_filter": GaussianBlurConfigWidget,
        "morphology": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.morphology": EdgeDetectionConfigWidget,
        "threshold": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.threshold": EdgeDetectionConfigWidget,
        "color_space": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.color_space": EdgeDetectionConfigWidget,
        "histogram": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.histogram": EdgeDetectionConfigWidget,
        "contrast": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.contrast": EdgeDetectionConfigWidget,
        "noise_reduction": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.noise_reduction": GaussianBlurConfigWidget,

        # 特征检测 (7种)
        "template_matching": TemplateMatchingConfigWidget,
        "feature_detection.template_matching": TemplateMatchingConfigWidget,
        "contour_detection": ContourDetectionConfigWidget,
        "feature_detection.contour_detection": ContourDetectionConfigWidget,
        "corner_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.corner_detection": EdgeDetectionConfigWidget,
        "blob_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "feature_detection.blob_detection": ContourDetectionConfigWidget,
        "line_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.line_detection": EdgeDetectionConfigWidget,
        "circle_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "feature_detection.circle_detection": ContourDetectionConfigWidget,
        "keypoint_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.keypoint_detection": EdgeDetectionConfigWidget,

        # 目标检测 (5种)
        "color_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "object_detection.color_detection": ContourDetectionConfigWidget,
        "shape_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "object_detection.shape_detection": ContourDetectionConfigWidget,
        "text_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.text_detection": TemplateMatchingConfigWidget,
        "barcode_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.barcode_detection": TemplateMatchingConfigWidget,
        "face_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.face_detection": TemplateMatchingConfigWidget,

        # 测量算法 (5种)
        "distance_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.distance_measurement": EdgeDetectionConfigWidget,
        "angle_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.angle_measurement": EdgeDetectionConfigWidget,
        "area_measurement": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "measurement.area_measurement": ContourDetectionConfigWidget,
        "geometry_analysis": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "measurement.geometry_analysis": ContourDetectionConfigWidget,
        "dimension_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.dimension_measurement": EdgeDetectionConfigWidget,

        # 深度学习 (4种)
        "yolo_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "deep_learning.yolo_detection": TemplateMatchingConfigWidget,
        "classification": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "deep_learning.classification": TemplateMatchingConfigWidget,
        "segmentation": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "deep_learning.segmentation": ContourDetectionConfigWidget,
        "pose_estimation": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "deep_learning.pose_estimation": EdgeDetectionConfigWidget,

        # 位置修正 (5种)
        "affine_transform": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.affine_transform": EdgeDetectionConfigWidget,
        "perspective_transform": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.perspective_transform": EdgeDetectionConfigWidget,
        "rotation_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.rotation_correction": EdgeDetectionConfigWidget,
        "translation_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.translation_correction": EdgeDetectionConfigWidget,
        "scale_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.scale_correction": EdgeDetectionConfigWidget,
    }

    @classmethod
    def create_config_widget(cls, algorithm_name: str, parent=None) -> BaseAlgorithmConfigWidget:
        """创建算法配置界面"""
        widget_class = cls.WIDGET_MAPPING.get(algorithm_name)

        if widget_class:
            logger.info(f"创建专用配置界面: {algorithm_name}")
            return widget_class(algorithm_name, parent)
        else:
            logger.warning(f"未找到专用配置界面，使用通用界面: {algorithm_name}")
            return cls._create_generic_widget(algorithm_name, parent)

    @classmethod
    def _create_generic_widget(cls, algorithm_name: str, parent=None) -> BaseAlgorithmConfigWidget:
        """创建通用配置界面"""
        return GenericAlgorithmConfigWidget(algorithm_name, parent)

    @classmethod
    def register_widget(cls, algorithm_name: str, widget_class):
        """注册新的算法配置界面"""
        cls.WIDGET_MAPPING[algorithm_name] = widget_class
        logger.info(f"注册算法配置界面: {algorithm_name} -> {widget_class.__name__}")

    @classmethod
    def get_supported_algorithms(cls) -> List[str]:
        """获取支持的算法列表"""
        return list(cls.WIDGET_MAPPING.keys())


class GenericAlgorithmConfigWidget(BaseAlgorithmConfigWidget):
    """通用算法配置界面"""

    def __init__(self, algorithm_name: str, parent=None):
        self.algorithm_schema = None
        super().__init__(algorithm_name, parent)

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 参数配置区域
        self.param_group = QGroupBox("算法参数")
        self.param_layout = QFormLayout(self.param_group)
        layout.addWidget(self.param_group)

        # 预览按钮
        preview_btn = ModernButton("预览效果", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

        # 尝试加载算法参数模式
        self._load_algorithm_schema()

    def _load_algorithm_schema(self):
        """加载算法参数模式"""
        try:
            # 尝试从算法注册表获取参数模式
            from wirevsion.algorithms.registry import AlgorithmRegistry
            registry = AlgorithmRegistry()

            # 解析算法名称
            if "." in self.algorithm_name:
                category, name = self.algorithm_name.split(".", 1)
                algorithm_class = registry.get_algorithm_class(category, name)
            else:
                # 尝试在所有类别中查找
                algorithm_class = None
                for category in ["image_source", "image_processing", "feature_detection",
                               "object_detection", "measurement", "deep_learning", "position_correction"]:
                    try:
                        algorithm_class = registry.get_algorithm_class(category, self.algorithm_name)
                        if algorithm_class:
                            break
                    except:
                        continue

            if algorithm_class:
                algorithm_instance = algorithm_class()
                self.algorithm_schema = algorithm_instance.get_parameter_schema()
                self._create_parameter_widgets()
                logger.info(f"加载算法参数模式: {self.algorithm_name}")
            else:
                logger.warning(f"未找到算法类: {self.algorithm_name}")
                self._create_default_parameters()

        except Exception as e:
            logger.error(f"加载算法参数模式失败: {e}")
            self._create_default_parameters()

    def _create_parameter_widgets(self):
        """根据参数模式创建控件"""
        if not self.algorithm_schema:
            return

        for param_name, param_config in self.algorithm_schema.items():
            param_type = param_config.get("type", "string")
            description = param_config.get("description", param_name)
            default_value = param_config.get("default")

            widget = self._create_parameter_widget(param_name, param_type, param_config, default_value)
            if widget:
                self.param_layout.addRow(f"{description}:", widget)

    def _create_parameter_widget(self, param_name: str, param_type: str,
                                param_config: Dict[str, Any], default_value: Any):
        """创建参数控件"""
        widget = None

        if param_type == "integer":
            widget = QSpinBox()
            widget.setRange(param_config.get("minimum", -999999),
                          param_config.get("maximum", 999999))
            if default_value is not None:
                widget.setValue(default_value)
            widget.valueChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "number":
            widget = QDoubleSpinBox()
            widget.setRange(param_config.get("minimum", -999999.0),
                          param_config.get("maximum", 999999.0))
            widget.setDecimals(param_config.get("decimals", 2))
            if default_value is not None:
                widget.setValue(default_value)
            widget.valueChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "boolean":
            widget = QCheckBox()
            if default_value is not None:
                widget.setChecked(default_value)
            widget.toggled.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "string":
            if "enum" in param_config:
                widget = QComboBox()
                widget.addItems(param_config["enum"])
                if default_value is not None:
                    widget.setCurrentText(str(default_value))
                widget.currentTextChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))
            else:
                widget = QLineEdit()
                if default_value is not None:
                    widget.setText(str(default_value))
                widget.textChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        return widget

    def _create_default_parameters(self):
        """创建默认参数"""
        # 添加一个启用开关
        enable_widget = QCheckBox()
        enable_widget.setChecked(True)
        enable_widget.toggled.connect(lambda v: self._emit_parameter_changed("enabled", v))
        self.param_layout.addRow("启用算法:", enable_widget)

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)