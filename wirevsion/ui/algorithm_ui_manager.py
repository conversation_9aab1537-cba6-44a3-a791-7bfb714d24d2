"""
算法UI管理器 - 管理所有算法的UI组件和模板

功能：
- 算法UI组件注册和管理
- 算法模板管理
- 参数预设管理
- UI组件动态创建
"""

from typing import Dict, Any, List, Optional, Type, Callable
import json
import os
from pathlib import Path

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTabWidget, QFrame, QScrollArea, QSplitter, QGroupBox,
    QGridLayout, QDialog, QDialogButtonBox, QTextEdit,
    QListWidget, QListWidgetItem, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from PyQt5.QtGui import QFont, QIcon

from loguru import logger
from .algorithm_parameter_widgets import AlgorithmParameterWidget
from ..algorithms.registry import AlgorithmRegistry


class AlgorithmUIManager(QObject):
    """算法UI管理器"""
    
    ui_created = pyqtSignal(str, QWidget)  # 算法名, UI控件
    template_loaded = pyqtSignal(str, dict)  # 模板名, 参数
    
    def __init__(self):
        super().__init__()
        
        self.algorithm_registry = AlgorithmRegistry()
        self.ui_widgets = {}  # 算法名 -> UI控件
        self.templates = {}   # 算法名 -> 模板列表
        self.presets = {}     # 算法名 -> 预设参数
        
        self._load_templates()
        self._load_presets()
    
    def create_algorithm_ui(self, algorithm_name: str) -> Optional[QWidget]:
        """创建算法UI"""
        try:
            # 获取算法实例
            algorithm = self.algorithm_registry.get_algorithm(algorithm_name)
            if not algorithm:
                logger.error(f"算法不存在: {algorithm_name}")
                return None
            
            # 获取参数模式和默认参数
            parameter_schema = algorithm.get_parameter_schema()
            default_parameters = algorithm.get_default_parameters()
            display_name = algorithm.get_display_name()
            
            # 创建参数控件
            param_widget = AlgorithmParameterWidget(
                display_name, parameter_schema, default_parameters
            )
            
            # 创建完整的UI
            ui_widget = self._create_complete_ui(algorithm, param_widget)
            
            # 存储引用
            self.ui_widgets[algorithm_name] = ui_widget
            
            # 发射信号
            self.ui_created.emit(algorithm_name, ui_widget)
            
            return ui_widget
            
        except Exception as e:
            logger.error(f"创建算法UI失败 {algorithm_name}: {e}")
            return None
    
    def _create_complete_ui(self, algorithm, param_widget: AlgorithmParameterWidget) -> QWidget:
        """创建完整的算法UI"""
        container = QWidget()
        layout = QVBoxLayout(container)
        
        # 算法信息头部
        header = self._create_algorithm_header(algorithm)
        layout.addWidget(header)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：参数配置
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout(left_panel)
        
        # 参数控件
        left_layout.addWidget(param_widget)
        
        # 模板和预设
        template_widget = self._create_template_widget(algorithm.get_algorithm_name())
        left_layout.addWidget(template_widget)
        
        main_splitter.addWidget(left_panel)
        
        # 右侧：预览和结果
        right_panel = self._create_preview_panel(algorithm)
        main_splitter.addWidget(right_panel)
        
        # 设置分割比例
        main_splitter.setSizes([300, 400])
        
        layout.addWidget(main_splitter)
        
        # 底部操作栏
        bottom_bar = self._create_bottom_bar(algorithm)
        layout.addWidget(bottom_bar)
        
        return container
    
    def _create_algorithm_header(self, algorithm) -> QWidget:
        """创建算法信息头部"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        header.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        # 算法图标（如果有）
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setStyleSheet("""
            QLabel {
                background-color: #3498db;
                border-radius: 24px;
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setText(algorithm.get_display_name()[0])  # 首字母
        layout.addWidget(icon_label)
        
        # 算法信息
        info_layout = QVBoxLayout()
        
        # 名称
        name_label = QLabel(algorithm.get_display_name())
        name_label.setFont(QFont("Arial", 14, QFont.Bold))
        name_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(name_label)
        
        # 描述
        desc_label = QLabel(algorithm.get_description())
        desc_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        desc_label.setWordWrap(True)
        info_layout.addWidget(desc_label)
        
        # 类型
        type_label = QLabel(f"类型: {algorithm.get_algorithm_type().value}")
        type_label.setStyleSheet("color: #95a5a6; font-size: 11px;")
        info_layout.addWidget(type_label)
        
        layout.addLayout(info_layout, 1)
        
        return header
    
    def _create_template_widget(self, algorithm_name: str) -> QWidget:
        """创建模板和预设控件"""
        group = QGroupBox("模板和预设")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout(group)
        
        # 模板列表
        template_list = QListWidget()
        template_list.setMaximumHeight(100)
        
        # 加载模板
        templates = self.templates.get(algorithm_name, [])
        for template in templates:
            item = QListWidgetItem(template["name"])
            item.setData(Qt.UserRole, template)
            template_list.addItem(item)
        
        layout.addWidget(QLabel("可用模板:"))
        layout.addWidget(template_list)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        
        load_btn = QPushButton("加载模板")
        save_btn = QPushButton("保存模板")
        
        btn_layout.addWidget(load_btn)
        btn_layout.addWidget(save_btn)
        layout.addLayout(btn_layout)
        
        # 连接信号
        load_btn.clicked.connect(lambda: self._load_template(template_list, algorithm_name))
        save_btn.clicked.connect(lambda: self._save_template(algorithm_name))
        
        return group
    
    def _create_preview_panel(self, algorithm) -> QWidget:
        """创建预览面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 预览标题
        title = QLabel("算法预览")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(title)
        
        # 预览区域
        preview_area = QLabel("点击预览按钮查看效果")
        preview_area.setMinimumHeight(300)
        preview_area.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #7f8c8d;
                font-size: 14px;
            }
        """)
        preview_area.setAlignment(Qt.AlignCenter)
        layout.addWidget(preview_area)
        
        # 结果信息
        result_info = QTextEdit()
        result_info.setMaximumHeight(100)
        result_info.setPlaceholderText("算法执行结果将显示在这里...")
        layout.addWidget(result_info)
        
        return panel
    
    def _create_bottom_bar(self, algorithm) -> QWidget:
        """创建底部操作栏"""
        bar = QFrame()
        bar.setFrameStyle(QFrame.StyledPanel)
        bar.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
            }
        """)
        
        layout = QHBoxLayout(bar)
        
        # 状态信息
        status_label = QLabel("就绪")
        status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        layout.addWidget(status_label)
        
        layout.addStretch()
        
        # 操作按钮
        execute_btn = QPushButton("执行算法")
        execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(execute_btn)
        
        return bar
    
    def _load_template(self, template_list: QListWidget, algorithm_name: str):
        """加载模板"""
        current_item = template_list.currentItem()
        if current_item:
            template = current_item.data(Qt.UserRole)
            self.template_loaded.emit(algorithm_name, template["parameters"])
    
    def _save_template(self, algorithm_name: str):
        """保存模板"""
        # 这里应该获取当前参数并保存为模板
        # 为了简化，这里只是显示一个消息
        QMessageBox.information(None, "保存模板", "模板保存功能待实现")
    
    def _load_templates(self):
        """加载算法模板"""
        template_dir = Path("resources/templates")
        if template_dir.exists():
            for template_file in template_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                        algorithm_name = template_data.get("algorithm")
                        if algorithm_name:
                            if algorithm_name not in self.templates:
                                self.templates[algorithm_name] = []
                            self.templates[algorithm_name].append(template_data)
                except Exception as e:
                    logger.error(f"加载模板失败 {template_file}: {e}")
    
    def _load_presets(self):
        """加载参数预设"""
        preset_dir = Path("resources/presets")
        if preset_dir.exists():
            for preset_file in preset_dir.glob("*.json"):
                try:
                    with open(preset_file, 'r', encoding='utf-8') as f:
                        preset_data = json.load(f)
                        algorithm_name = preset_data.get("algorithm")
                        if algorithm_name:
                            self.presets[algorithm_name] = preset_data.get("presets", [])
                except Exception as e:
                    logger.error(f"加载预设失败 {preset_file}: {e}")
    
    def get_algorithm_ui(self, algorithm_name: str) -> Optional[QWidget]:
        """获取算法UI"""
        return self.ui_widgets.get(algorithm_name)
    
    def get_available_templates(self, algorithm_name: str) -> List[Dict[str, Any]]:
        """获取可用模板"""
        return self.templates.get(algorithm_name, [])
    
    def get_available_presets(self, algorithm_name: str) -> List[Dict[str, Any]]:
        """获取可用预设"""
        return self.presets.get(algorithm_name, [])
