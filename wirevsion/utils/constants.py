#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目常量定义模块
统一管理项目中使用的常量，避免硬编码
"""

import cv2
from enum import Enum, IntEnum
from typing import Dict, Tuple


class UIConstants:
    """UI相关常量"""
    
    # 窗口尺寸
    MIN_WINDOW_WIDTH = 1200
    MIN_WINDOW_HEIGHT = 800
    
    # 图像显示相关
    MIN_IMAGE_PREVIEW_WIDTH = 400
    MIN_IMAGE_PREVIEW_HEIGHT = 300
    MAX_IMAGE_DISPLAY_SIZE = 800
    
    # 缩放范围
    MIN_ZOOM_PERCENT = 10
    MAX_ZOOM_PERCENT = 500
    DEFAULT_ZOOM_PERCENT = 100
    
    # ROI相关
    MIN_ROI_SIZE = 10
    DEFAULT_ROI_COLOR = (255, 0, 255)  # 紫色
    
    # 浅色主题样式颜色
    BACKGROUND_COLOR = "#f8f9fa"        # 浅灰白色背景
    FOREGROUND_COLOR = "#212529"        # 深灰色文字
    BUTTON_COLOR = "#ffffff"            # 白色按钮
    BUTTON_HOVER_COLOR = "#e9ecef"      # 浅灰色悬停
    BUTTON_PRESSED_COLOR = "#dee2e6"    # 更深的灰色按下
    BUTTON_DISABLED_COLOR = "#f8f9fa"   # 禁用状态
    BORDER_COLOR = "#dee2e6"            # 边框颜色
    ACCENT_COLOR = "#0d6efd"            # 蓝色强调色
    
    # 字体设置
    DEFAULT_FONT_SIZE = 12
    TITLE_FONT_SIZE = 14
    
    # 间距设置
    DEFAULT_SPACING = 5
    GROUP_SPACING = 10
    BUTTON_PADDING = 8


class ImageProcessingConstants:
    """图像处理相关常量"""
    
    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = [
        "*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.gif"
    ]
    
    # 图像处理参数
    BINARY_THRESHOLD = 127
    BINARY_MAX_VALUE = 255
    MIN_CONTOUR_AREA = 50
    
    # 模板匹配相关
    DEFAULT_TEMPLATE_THRESHOLD = 0.8
    MIN_TEMPLATE_THRESHOLD = 0.1
    MAX_TEMPLATE_THRESHOLD = 1.0
    
    # 轮廓匹配相关
    DEFAULT_CONTOUR_THRESHOLD = 0.1
    MAX_SHAPE_MATCH_DISTANCE = 1.0
    
    # 颜色检测相关
    DEFAULT_COLOR_TOLERANCE = 20
    MIN_COLOR_TOLERANCE = 1
    MAX_COLOR_TOLERANCE = 100


class MatchingMethod(IntEnum):
    """模板匹配方法枚举"""
    SQDIFF = cv2.TM_SQDIFF
    SQDIFF_NORMED = cv2.TM_SQDIFF_NORMED
    CCORR = cv2.TM_CCORR
    CCORR_NORMED = cv2.TM_CCORR_NORMED
    CCOEFF = cv2.TM_CCOEFF
    CCOEFF_NORMED = cv2.TM_CCOEFF_NORMED


class MatchingMethodNames:
    """模板匹配方法名称映射"""
    
    METHOD_NAMES: Dict[int, str] = {
        cv2.TM_SQDIFF: "平方差匹配法(CV_TM_SQDIFF)",
        cv2.TM_SQDIFF_NORMED: "归一化平方差匹配法(CV_TM_SQDIFF_NORMED)",
        cv2.TM_CCORR: "相关匹配法(CV_TM_CCORR)",
        cv2.TM_CCORR_NORMED: "归一化相关匹配法(CV_TM_CCORR_NORMED)",
        cv2.TM_CCOEFF: "相关系数匹配法(CV_TM_CCOEFF)",
        cv2.TM_CCOEFF_NORMED: "归一化相关系数匹配法(CV_TM_CCOEFF_NORMED)"
    }
    
    @classmethod
    def get_method_name(cls, method: int) -> str:
        """
        获取匹配方法名称
        
        Args:
            method: 匹配方法代码
            
        Returns:
            str: 方法名称
        """
        return cls.METHOD_NAMES.get(method, "未知方法")
    
    @classmethod
    def get_all_methods(cls) -> Dict[str, int]:
        """
        获取所有可用的匹配方法
        
        Returns:
            Dict[str, int]: 方法名称到代码的映射
        """
        return {name: method for method, name in cls.METHOD_NAMES.items()}


class CameraConstants:
    """相机相关常量"""
    
    # 支持的像素格式
    PIXEL_FORMATS = [
        "MONO8", "MONO12", "RGB8", "BGR8", "YUV422"
    ]
    
    # 默认相机参数
    DEFAULT_RESOLUTION = (1280, 720)
    DEFAULT_FPS = 60
    DEFAULT_EXPOSURE = 100
    DEFAULT_GAIN = 0
    
    # 参数范围
    MIN_EXPOSURE = 1
    MAX_EXPOSURE = 10000
    MIN_GAIN = 0
    MAX_GAIN = 100
    
    # 捕获超时时间（毫秒）
    CAPTURE_TIMEOUT_MS = 5000


class FileConstants:
    """文件相关常量"""
    
    # 配置文件路径
    CONFIG_DIR = "config"
    WORKFLOW_CONFIG_FILE = "workflow_config.yaml"
    APP_CONFIG_FILE = "app_config.yaml"
    CAMERA_CONFIG_FILE = "camera_config.yaml"
    
    # 日志配置
    LOG_DIR = "logs"
    LOG_FILE_FORMAT = "wirevsion_{time:YYYY-MM-DD}.log"
    MAX_LOG_FILE_SIZE = "10 MB"
    LOG_RETENTION = "30 days"
    
    # 图像保存
    IMAGE_SAVE_DIR = "saved_images"
    IMAGE_FILE_FORMAT = "image_{timestamp}.png"
    
    # 结果保存
    RESULT_SAVE_DIR = "results"
    RESULT_FILE_FORMAT = "result_{timestamp}.json"


class ErrorMessages:
    """错误消息常量"""
    
    # 通用错误
    UNKNOWN_ERROR = "未知错误"
    OPERATION_CANCELLED = "操作已取消"
    OPERATION_TIMEOUT = "操作超时"
    
    # 图像相关错误
    IMAGE_LOAD_FAILED = "图像加载失败"
    IMAGE_SAVE_FAILED = "图像保存失败"
    IMAGE_FORMAT_UNSUPPORTED = "不支持的图像格式"
    IMAGE_TOO_LARGE = "图像过大"
    IMAGE_TOO_SMALL = "图像过小"
    NO_IMAGE_AVAILABLE = "没有可用的图像"
    
    # 模板匹配错误
    TEMPLATE_NOT_SET = "未设置模板图像"
    TEMPLATE_TOO_LARGE = "模板图像过大"
    MATCHING_FAILED = "模板匹配失败"
    NO_MATCH_FOUND = "未找到匹配结果"
    
    # 相机相关错误
    CAMERA_NOT_FOUND = "未找到相机"
    CAMERA_CONNECTION_FAILED = "相机连接失败"
    CAMERA_CAPTURE_FAILED = "相机捕获失败"
    CAMERA_ALREADY_OPENED = "相机已打开"
    CAMERA_NOT_OPENED = "相机未打开"
    
    # 配置相关错误
    CONFIG_LOAD_FAILED = "配置加载失败"
    CONFIG_SAVE_FAILED = "配置保存失败"
    CONFIG_INVALID = "配置格式无效"
    
    # ROI相关错误
    ROI_TOO_SMALL = "ROI区域过小"
    ROI_OUT_OF_BOUNDS = "ROI区域超出图像范围"
    ROI_NOT_SET = "未设置ROI区域"


class SuccessMessages:
    """成功消息常量"""
    
    # 通用成功
    OPERATION_SUCCESS = "操作成功"
    
    # 图像相关成功
    IMAGE_LOADED = "图像加载成功"
    IMAGE_SAVED = "图像保存成功"
    
    # 模板匹配成功
    TEMPLATE_SET = "模板设置成功"
    MATCHING_COMPLETED = "模板匹配完成"
    MATCH_FOUND = "找到匹配结果"
    
    # 相机相关成功
    CAMERA_CONNECTED = "相机连接成功"
    CAMERA_DISCONNECTED = "相机断开成功"
    IMAGE_CAPTURED = "图像捕获成功"
    
    # 配置相关成功
    CONFIG_LOADED = "配置加载成功"
    CONFIG_SAVED = "配置保存成功"
    
    # ROI相关成功
    ROI_SET = "ROI设置成功"
    ROI_CLEARED = "ROI清除成功"


class StatusMessages:
    """状态消息常量"""
    
    # 系统状态
    SYSTEM_READY = "系统就绪"
    SYSTEM_BUSY = "系统忙碌"
    SYSTEM_ERROR = "系统错误"
    
    # 工作模式
    CONFIG_MODE = "配置模式"
    RUN_MODE = "运行模式"
    
    # 处理状态
    PROCESSING = "处理中"
    WAITING = "等待中"
    COMPLETED = "已完成"
    STOPPED = "已停止"
    
    # 相机状态
    CAMERA_DISCONNECTED = "相机未连接"
    CAMERA_CONNECTED = "相机已连接"
    CAMERA_CAPTURING = "正在捕获"


class ValidationRules:
    """验证规则常量"""
    
    # 文件名验证
    INVALID_FILENAME_CHARS = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    MAX_FILENAME_LENGTH = 255
    
    # 数值范围验证
    MIN_IMAGE_WIDTH = 10
    MIN_IMAGE_HEIGHT = 10
    MAX_IMAGE_WIDTH = 10000
    MAX_IMAGE_HEIGHT = 10000
    
    # 字符串长度验证
    MIN_NAME_LENGTH = 1
    MAX_NAME_LENGTH = 100
    MAX_DESCRIPTION_LENGTH = 500


class PerformanceConstants:
    """性能相关常量"""
    
    # 线程池大小
    MAX_WORKER_THREADS = 4
    
    # 内存限制
    MAX_IMAGE_MEMORY_MB = 500
    
    # 超时设置
    OPERATION_TIMEOUT_SECONDS = 30
    NETWORK_TIMEOUT_SECONDS = 10
    
    # 缓存设置
    MAX_CACHE_SIZE = 100
    CACHE_EXPIRE_MINUTES = 30
    
    # 图像处理优化
    ENABLE_MULTITHREADING = True
    USE_GPU_ACCELERATION = False  # 根据硬件情况调整 