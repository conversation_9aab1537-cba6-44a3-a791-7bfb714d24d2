#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测量算法模块

提供各种测量算法：距离测量、角度测量、面积测量、几何分析等
"""

import time
import cv2
import numpy as np
import math
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils


class DistanceMeasurementAlgorithm(BaseAlgorithm):
    """距离测量算法 - 使用装饰器优化版本"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT
    
    def get_algorithm_name(self) -> str:
        return "distance_measurement"
    
    def get_display_name(self) -> str:
        return "距离测量"
    
    def get_description(self) -> str:
        return "测量图像中两点之间的距离"
    
    def get_icon_path(self) -> str:
        return "resources/icons/distance_measurement.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": 1.0,  # 每单位的像素数
            "unit": "pixel",         # 测量单位
            "line_thickness": 2,     # 绘制线条粗细
            "point_radius": 5,       # 点标记半径
            "text_size": 0.7,       # 文字大小
            "precision": 2          # 小数点精度
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1000.0,
                "description": "每单位对应的像素数"
            },
            "unit": {
                "type": "string",
                "enum": ["pixel", "mm", "cm", "inch"],
                "description": "测量单位"
            },
            "line_thickness": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "绘制线条粗细"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("points", DataType.POINTS, True, "测量点对")
        self.add_output_connection("distances", DataType.RESULTS, "距离测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记测量的图像")
    
    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行距离测量 - 使用装饰器优化版本"""
        # 装饰器已处理输入验证、时间记录、异常处理等
        
        image = inputs["image"]
        points = inputs["points"]
        parameters = config.parameters
        
        # 参数提取
        pixels_per_unit = parameters.get("pixels_per_unit", 1.0)
        unit = parameters.get("unit", "pixel")
        line_thickness = parameters.get("line_thickness", 2)
        point_radius = parameters.get("point_radius", 5)
        text_size = parameters.get("text_size", 0.7)
        precision = parameters.get("precision", 2)
        
        # 处理点数据
        if isinstance(points, list) and len(points) >= 2:
            point_pairs = []
            # 假设points是成对的点 [p1, p2, p3, p4, ...] -> [(p1,p2), (p3,p4), ...]
            for i in range(0, len(points) - 1, 2):
                if i + 1 < len(points):
                    point_pairs.append((points[i], points[i + 1]))
        else:
            # 如果只有两个点，直接使用
            if len(points) >= 2:
                point_pairs = [(points[0], points[1])]
            else:
                point_pairs = []
        
        # 计算距离
        distances = []
        result_image = image.copy()
        
        for idx, (point1, point2) in enumerate(point_pairs):
            # 提取坐标
            if isinstance(point1, dict):
                x1, y1 = point1.get("x", 0), point1.get("y", 0)
            else:
                x1, y1 = point1[0], point1[1]
            
            if isinstance(point2, dict):
                x2, y2 = point2.get("x", 0), point2.get("y", 0)
            else:
                x2, y2 = point2[0], point2[1]
            
            # 计算像素距离
            pixel_distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            
            # 转换为实际单位
            actual_distance = pixel_distance / pixels_per_unit
            
            # 记录结果
            distance_info = {
                "point1": {"x": x1, "y": y1},
                "point2": {"x": x2, "y": y2},
                "pixel_distance": round(pixel_distance, precision),
                "actual_distance": round(actual_distance, precision),
                "unit": unit
            }
            distances.append(distance_info)
            
            # 绘制测量线和点
            pt1 = (int(x1), int(y1))
            pt2 = (int(x2), int(y2))
            
            # 绘制线条
            cv2.line(result_image, pt1, pt2, (0, 255, 0), line_thickness)
            
            # 绘制端点
            cv2.circle(result_image, pt1, point_radius, (255, 0, 0), -1)
            cv2.circle(result_image, pt2, point_radius, (255, 0, 0), -1)
            
            # 绘制距离文本
            mid_x = int((x1 + x2) / 2)
            mid_y = int((y1 + y2) / 2) - 10
            
            distance_text = f"{actual_distance:.{precision}f} {unit}"
            cv2.putText(result_image, distance_text, (mid_x, mid_y),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (0, 255, 0), 2)
        
        return create_success_result(
            data={
                "distances": distances,
                "measurement_count": len(distances),
                "unit": unit,
                "pixels_per_unit": pixels_per_unit
            },
            image=result_image,
            message=f"距离测量完成，测量了 {len(distances)} 段距离"
        )


class AngleMeasurementAlgorithm(BaseAlgorithm):
    """角度测量算法 - 使用装饰器优化版本"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT
    
    def get_algorithm_name(self) -> str:
        return "angle_measurement"
    
    def get_display_name(self) -> str:
        return "角度测量"
    
    def get_description(self) -> str:
        return "测量图像中三点组成的角度"
    
    def get_icon_path(self) -> str:
        return "resources/icons/angle_measurement.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "angle_unit": "degree",     # degree 或 radian
            "line_thickness": 2,        # 绘制线条粗细
            "arc_radius": 30,          # 角度弧半径
            "text_size": 0.7,          # 文字大小
            "precision": 1             # 小数点精度
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "angle_unit": {
                "type": "string",
                "enum": ["degree", "radian"],
                "description": "角度单位"
            },
            "line_thickness": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "绘制线条粗细"
            },
            "arc_radius": {
                "type": "integer",
                "minimum": 10,
                "maximum": 100,
                "description": "角度弧显示半径"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("points", DataType.POINTS, True, "角度点(顶点,点1,点2)")
        self.add_output_connection("angles", DataType.RESULTS, "角度测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记角度的图像")
    
    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行角度测量 - 使用装饰器优化版本"""
        image = inputs["image"]
        points = inputs["points"]
        parameters = config.parameters
        
        # 参数提取
        angle_unit = parameters.get("angle_unit", "degree")
        line_thickness = parameters.get("line_thickness", 2)
        arc_radius = parameters.get("arc_radius", 30)
        text_size = parameters.get("text_size", 0.7)
        precision = parameters.get("precision", 1)
        
        # 处理点数据 - 每三个点组成一个角度 (顶点, 点1, 点2)
        if isinstance(points, list) and len(points) >= 3:
            angle_groups = []
            for i in range(0, len(points) - 2, 3):
                if i + 2 < len(points):
                    angle_groups.append((points[i], points[i + 1], points[i + 2]))
        else:
            angle_groups = []
        
        # 计算角度
        angles = []
        result_image = image.copy()
        
        for idx, (vertex, point1, point2) in enumerate(angle_groups):
            # 提取坐标
            if isinstance(vertex, dict):
                vx, vy = vertex.get("x", 0), vertex.get("y", 0)
            else:
                vx, vy = vertex[0], vertex[1]
            
            if isinstance(point1, dict):
                x1, y1 = point1.get("x", 0), point1.get("y", 0)
            else:
                x1, y1 = point1[0], point1[1]
            
            if isinstance(point2, dict):
                x2, y2 = point2.get("x", 0), point2.get("y", 0)
            else:
                x2, y2 = point2[0], point2[1]
            
            # 计算向量
            vector1 = (x1 - vx, y1 - vy)
            vector2 = (x2 - vx, y2 - vy)
            
            # 计算角度 (弧度)
            dot_product = vector1[0] * vector2[0] + vector1[1] * vector2[1]
            magnitude1 = math.sqrt(vector1[0] ** 2 + vector1[1] ** 2)
            magnitude2 = math.sqrt(vector2[0] ** 2 + vector2[1] ** 2)
            
            if magnitude1 == 0 or magnitude2 == 0:
                angle_rad = 0
            else:
                cos_angle = dot_product / (magnitude1 * magnitude2)
                # 防止数值误差导致的域错误
                cos_angle = max(-1, min(1, cos_angle))
                angle_rad = math.acos(cos_angle)
            
            # 转换单位
            if angle_unit == "degree":
                angle_value = math.degrees(angle_rad)
                unit_symbol = "°"
            else:
                angle_value = angle_rad
                unit_symbol = " rad"
            
            # 记录结果
            angle_info = {
                "vertex": {"x": vx, "y": vy},
                "point1": {"x": x1, "y": y1},
                "point2": {"x": x2, "y": y2},
                "angle_radian": round(angle_rad, precision + 2),
                "angle_value": round(angle_value, precision),
                "unit": angle_unit
            }
            angles.append(angle_info)
            
            # 绘制角度线
            vertex_pt = (int(vx), int(vy))
            pt1 = (int(x1), int(y1))
            pt2 = (int(x2), int(y2))
            
            # 绘制两条边
            cv2.line(result_image, vertex_pt, pt1, (0, 255, 255), line_thickness)
            cv2.line(result_image, vertex_pt, pt2, (0, 255, 255), line_thickness)
            
            # 绘制角度弧（简化版）
            start_angle = math.degrees(math.atan2(y1 - vy, x1 - vx))
            end_angle = math.degrees(math.atan2(y2 - vy, x2 - vx))
            
            # 确保角度为正值且按逆时针方向
            if end_angle < start_angle:
                end_angle += 360
            
            cv2.ellipse(result_image, vertex_pt, (arc_radius, arc_radius), 
                       0, start_angle, end_angle, (255, 0, 255), 2)
            
            # 绘制顶点
            cv2.circle(result_image, vertex_pt, 5, (255, 0, 0), -1)
            cv2.circle(result_image, pt1, 3, (0, 255, 0), -1)
            cv2.circle(result_image, pt2, 3, (0, 255, 0), -1)
            
            # 绘制角度文本
            text_x = int(vx + arc_radius * 1.2)
            text_y = int(vy)
            
            angle_text = f"{angle_value:.{precision}f}{unit_symbol}"
            cv2.putText(result_image, angle_text, (text_x, text_y),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (255, 0, 255), 2)
        
        return create_success_result(
            data={
                "angles": angles,
                "measurement_count": len(angles),
                "unit": angle_unit
            },
            image=result_image,
            message=f"角度测量完成，测量了 {len(angles)} 个角度"
        )


class AreaMeasurementAlgorithm(BaseAlgorithm):
    """面积测量算法 - 使用装饰器优化版本"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT
    
    def get_algorithm_name(self) -> str:
        return "area_measurement"
    
    def get_display_name(self) -> str:
        return "面积测量"
    
    def get_description(self) -> str:
        return "测量图像中轮廓或多边形的面积"
    
    def get_icon_path(self) -> str:
        return "resources/icons/area_measurement.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit_squared": 1.0,  # 每单位面积的像素数
            "unit": "pixel²",                # 面积单位
            "precision": 2,                  # 小数点精度
            "fill_alpha": 0.3               # 填充透明度
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit_squared": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1000000.0,
                "description": "每单位面积对应的像素数"
            },
            "unit": {
                "type": "string",
                "enum": ["pixel²", "mm²", "cm²", "inch²"],
                "description": "面积单位"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, True, "轮廓数据")
        self.add_output_connection("areas", DataType.RESULTS, "面积测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记面积的图像")
    
    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行面积测量 - 使用装饰器优化版本"""
        image = inputs["image"]
        contours = inputs["contours"]
        parameters = config.parameters
        
        # 参数提取
        pixels_per_unit_squared = parameters.get("pixels_per_unit_squared", 1.0)
        unit = parameters.get("unit", "pixel²")
        precision = parameters.get("precision", 2)
        fill_alpha = parameters.get("fill_alpha", 0.3)
        
        # 计算面积
        areas = []
        result_image = image.copy()
        overlay = image.copy()
        
        for idx, contour in enumerate(contours):
            # 转换轮廓格式
            if isinstance(contour, list):
                # 如果是点列表，转换为numpy数组
                contour_points = np.array([[pt["x"], pt["y"]] for pt in contour], dtype=np.int32)
            else:
                contour_points = contour
            
            # 计算像素面积
            pixel_area = cv2.contourArea(contour_points)
            
            # 转换为实际单位
            actual_area = pixel_area / pixels_per_unit_squared
            
            # 记录结果
            area_info = {
                "contour_index": idx,
                "pixel_area": round(pixel_area, precision),
                "actual_area": round(actual_area, precision),
                "unit": unit,
                "contour_points": len(contour_points)
            }
            areas.append(area_info)
            
            # 绘制轮廓
            cv2.drawContours(overlay, [contour_points], -1, (0, 255, 0), -1)
            cv2.drawContours(result_image, [contour_points], -1, (0, 255, 0), 2)
            
            # 绘制面积文本
            M = cv2.moments(contour_points)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                area_text = f"{actual_area:.{precision}f} {unit}"
                cv2.putText(result_image, area_text, (cx - 50, cy),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 应用透明度混合
        cv2.addWeighted(overlay, fill_alpha, result_image, 1 - fill_alpha, 0, result_image)
        
        return create_success_result(
            data={
                "areas": areas,
                "measurement_count": len(areas),
                "total_area": sum(area["actual_area"] for area in areas),
                "unit": unit
            },
            image=result_image,
            message=f"面积测量完成，测量了 {len(areas)} 个区域"
        )


# 其他测量算法的占位符实现
class GeometryAnalysisAlgorithm(BaseAlgorithm):
    """几何分析算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT
    
    def get_algorithm_name(self) -> str:
        return "geometry_analysis"
    
    def get_display_name(self) -> str:
        return "几何分析"
    
    def get_description(self) -> str:
        return "分析几何形状的各种属性"
    
    def get_icon_path(self) -> str:
        return "resources/icons/geometry_analysis.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("analysis", DataType.RESULTS, "几何分析结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("几何分析算法尚未实现")


class DimensionMeasurementAlgorithm(BaseAlgorithm):
    """尺寸测量算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT
    
    def get_algorithm_name(self) -> str:
        return "dimension_measurement"
    
    def get_display_name(self) -> str:
        return "尺寸测量"
    
    def get_description(self) -> str:
        return "测量物体的长、宽、高等尺寸"
    
    def get_icon_path(self) -> str:
        return "resources/icons/dimension_measurement.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("dimensions", DataType.RESULTS, "尺寸测量结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("尺寸测量算法尚未实现") 