"""
位置修正算法模块

提供各种位置修正算法：仿射变换、透视变换、旋转修正、平移修正、缩放修正等
"""

import time
import cv2
import numpy as np
import math
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)


class AffineTransformAlgorithm(BaseAlgorithm):
    """仿射变换算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION
    
    def get_algorithm_name(self) -> str:
        return "affine_transform"
    
    def get_display_name(self) -> str:
        return "仿射变换"
    
    def get_description(self) -> str:
        return "使用三点对应关系进行仿射变换校正"
    
    def get_icon_path(self) -> str:
        return "resources/icons/affine_transform.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "src_points": [[0, 0], [100, 0], [0, 100]],  # 源点
            "dst_points": [[0, 0], [100, 0], [0, 100]],  # 目标点
            "interpolation": "linear",  # linear, cubic, nearest
            "border_mode": "constant",  # constant, reflect, wrap, replicate
            "border_value": 0
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "src_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 3,
                "maxItems": 3,
                "description": "源点坐标 (3个点)"
            },
            "dst_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 3,
                "maxItems": 3,
                "description": "目标点坐标 (3个点)"
            },
            "interpolation": {
                "type": "string",
                "enum": ["linear", "cubic", "nearest"],
                "description": "插值方法"
            },
            "border_mode": {
                "type": "string",
                "enum": ["constant", "reflect", "wrap", "replicate"],
                "description": "边界处理模式"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("src_points", DataType.POINTS, False, "源点")
        self.add_input_connection("dst_points", DataType.POINTS, False, "目标点")
        
        self.add_output_connection("transformed_image", DataType.IMAGE, "变换后图像")
        self.add_output_connection("transform_matrix", DataType.RESULTS, "变换矩阵")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行仿射变换"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 获取变换点
            if "src_points" in inputs and "dst_points" in inputs:
                src_points = inputs["src_points"]
                dst_points = inputs["dst_points"]
            else:
                src_points = parameters.get("src_points", [[0, 0], [100, 0], [0, 100]])
                dst_points = parameters.get("dst_points", [[0, 0], [100, 0], [0, 100]])
            
            # 转换为numpy数组
            src_pts = np.float32(src_points[:3])  # 仿射变换需要3个点
            dst_pts = np.float32(dst_points[:3])
            
            # 计算仿射变换矩阵
            transform_matrix = cv2.getAffineTransform(src_pts, dst_pts)
            
            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }
            
            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }
            
            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)
            
            # 执行仿射变换
            h, w = image.shape[:2]
            transformed = cv2.warpAffine(
                image, transform_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "transform_matrix": transform_matrix.tolist(),
                    "src_points": src_points,
                    "dst_points": dst_points,
                    "interpolation": parameters.get("interpolation", "linear"),
                    "border_mode": parameters.get("border_mode", "constant")
                },
                image=transformed,
                message="仿射变换完成",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"仿射变换失败: {e}")
            return create_error_result(f"仿射变换失败: {e}", time.time() - start_time)


class PerspectiveTransformAlgorithm(BaseAlgorithm):
    """透视变换算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION
    
    def get_algorithm_name(self) -> str:
        return "perspective_transform"
    
    def get_display_name(self) -> str:
        return "透视变换"
    
    def get_description(self) -> str:
        return "使用四点对应关系进行透视变换校正"
    
    def get_icon_path(self) -> str:
        return "resources/icons/perspective_transform.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "src_points": [[0, 0], [100, 0], [100, 100], [0, 100]],  # 源点（四边形）
            "dst_points": [[0, 0], [100, 0], [100, 100], [0, 100]],  # 目标点
            "output_size": None,  # 输出图像尺寸，None表示与输入相同
            "interpolation": "linear",
            "border_mode": "constant",
            "border_value": 0
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "src_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 4,
                "maxItems": 4,
                "description": "源点坐标 (4个点)"
            },
            "dst_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 4,
                "maxItems": 4,
                "description": "目标点坐标 (4个点)"
            },
            "output_size": {
                "type": "array",
                "items": {"type": "integer"},
                "minItems": 2,
                "maxItems": 2,
                "description": "输出图像尺寸 [width, height]"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("src_points", DataType.POINTS, False, "源点")
        self.add_input_connection("dst_points", DataType.POINTS, False, "目标点")
        
        self.add_output_connection("transformed_image", DataType.IMAGE, "变换后图像")
        self.add_output_connection("transform_matrix", DataType.RESULTS, "变换矩阵")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行透视变换"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 获取变换点
            if "src_points" in inputs and "dst_points" in inputs:
                src_points = inputs["src_points"]
                dst_points = inputs["dst_points"]
            else:
                src_points = parameters.get("src_points", [[0, 0], [100, 0], [100, 100], [0, 100]])
                dst_points = parameters.get("dst_points", [[0, 0], [100, 0], [100, 100], [0, 100]])
            
            # 转换为numpy数组
            src_pts = np.float32(src_points[:4])  # 透视变换需要4个点
            dst_pts = np.float32(dst_points[:4])
            
            # 计算透视变换矩阵
            transform_matrix = cv2.getPerspectiveTransform(src_pts, dst_pts)
            
            # 确定输出尺寸
            output_size = parameters.get("output_size")
            if output_size:
                w, h = output_size
            else:
                h, w = image.shape[:2]
            
            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }
            
            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }
            
            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)
            
            # 执行透视变换
            transformed = cv2.warpPerspective(
                image, transform_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "transform_matrix": transform_matrix.tolist(),
                    "src_points": src_points,
                    "dst_points": dst_points,
                    "output_size": [w, h]
                },
                image=transformed,
                message="透视变换完成",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"透视变换失败: {e}")
            return create_error_result(f"透视变换失败: {e}", time.time() - start_time)


class RotationCorrectionAlgorithm(BaseAlgorithm):
    """旋转修正算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION
    
    def get_algorithm_name(self) -> str:
        return "rotation_correction"
    
    def get_display_name(self) -> str:
        return "旋转修正"
    
    def get_description(self) -> str:
        return "对图像进行旋转修正"
    
    def get_icon_path(self) -> str:
        return "resources/icons/rotation_correction.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "angle": 0.0,  # 旋转角度（度）
            "center": None,  # 旋转中心，None表示图像中心
            "scale": 1.0,  # 缩放比例
            "auto_detect_angle": False,  # 自动检测旋转角度
            "detection_method": "hough_lines",  # hough_lines, minAreaRect
            "interpolation": "linear",
            "border_mode": "constant",
            "border_value": 0
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "angle": {
                "type": "number",
                "minimum": -360,
                "maximum": 360,
                "description": "旋转角度（度）"
            },
            "center": {
                "type": "array",
                "items": {"type": "number"},
                "minItems": 2,
                "maxItems": 2,
                "description": "旋转中心 [x, y]"
            },
            "scale": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10,
                "description": "缩放比例"
            },
            "auto_detect_angle": {
                "type": "boolean",
                "description": "自动检测旋转角度"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "轮廓数据")
        
        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
        self.add_output_connection("rotation_info", DataType.RESULTS, "旋转信息")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行旋转修正"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            h, w = image.shape[:2]
            
            # 确定旋转角度
            angle = parameters.get("angle", 0.0)
            
            if parameters.get("auto_detect_angle", False):
                detected_angle = self._auto_detect_rotation_angle(image, parameters)
                if detected_angle is not None:
                    angle = detected_angle
            
            # 确定旋转中心
            center = parameters.get("center")
            if center is None:
                center = (w // 2, h // 2)
            else:
                center = tuple(center)
            
            scale = parameters.get("scale", 1.0)
            
            # 获取旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, scale)
            
            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }
            
            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }
            
            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)
            
            # 执行旋转
            rotated = cv2.warpAffine(
                image, rotation_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "rotation_info": {
                        "angle": angle,
                        "center": center,
                        "scale": scale,
                        "rotation_matrix": rotation_matrix.tolist()
                    }
                },
                image=rotated,
                message=f"旋转修正完成，角度: {angle:.2f}°",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"旋转修正失败: {e}")
            return create_error_result(f"旋转修正失败: {e}", time.time() - start_time)
    
    def _auto_detect_rotation_angle(self, image: np.ndarray, parameters: Dict[str, Any]) -> Optional[float]:
        """自动检测旋转角度"""
        try:
            method = parameters.get("detection_method", "hough_lines")
            
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            if method == "hough_lines":
                # 使用霍夫直线检测
                edges = cv2.Canny(gray, 50, 150)
                lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
                
                if lines is not None:
                    angles = []
                    for line in lines:
                        rho, theta = line[0]
                        angle = math.degrees(theta) - 90
                        if -45 <= angle <= 45:
                            angles.append(angle)
                    
                    if angles:
                        return np.median(angles)
            
            elif method == "minAreaRect":
                # 使用最小外接矩形
                contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if contours:
                    # 找到最大轮廓
                    largest_contour = max(contours, key=cv2.contourArea)
                    rect = cv2.minAreaRect(largest_contour)
                    return rect[2]  # 返回角度
            
            return None
            
        except Exception as e:
            logger.warning(f"自动检测旋转角度失败: {e}")
            return None


# 其他位置修正算法的占位符实现
class TranslationCorrectionAlgorithm(BaseAlgorithm):
    """平移修正算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION
    
    def get_algorithm_name(self) -> str:
        return "translation_correction"
    
    def get_display_name(self) -> str:
        return "平移修正"
    
    def get_description(self) -> str:
        return "对图像进行平移修正"
    
    def get_icon_path(self) -> str:
        return "resources/icons/translation_correction.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("平移修正算法尚未实现")


class ScaleCorrectionAlgorithm(BaseAlgorithm):
    """缩放修正算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION
    
    def get_algorithm_name(self) -> str:
        return "scale_correction"
    
    def get_display_name(self) -> str:
        return "缩放修正"
    
    def get_description(self) -> str:
        return "对图像进行缩放修正"
    
    def get_icon_path(self) -> str:
        return "resources/icons/scale_correction.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("缩放修正算法尚未实现") 