#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特征检测算法模块

提供各种特征检测算法：模板匹配、角点检测、轮廓检测等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, ROI, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils, TemplateMatchingUtils


class TemplateMatchingAlgorithm(BaseAlgorithm):
    """模板匹配算法 - 使用装饰器优化版本"""
    
    def __init__(self):
        super().__init__()
        self.templates = {}
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "template_matching"
    
    def get_display_name(self) -> str:
        return "模板匹配"
    
    def get_description(self) -> str:
        return "在图像中搜索匹配的模板"
    
    def get_icon_path(self) -> str:
        return "resources/icons/template_matching.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "TM_CCOEFF_NORMED",
            "threshold": 0.8,
            "max_matches": 10,
            "enable_multi_scale": True,
            "scale_range": [0.5, 2.0],
            "scale_step": 0.1,
            "enable_rotation": False,
            "rotation_range": [-45, 45],
            "rotation_step": 5
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["TM_CCOEFF", "TM_CCOEFF_NORMED", "TM_CCORR", 
                        "TM_CCORR_NORMED", "TM_SQDIFF", "TM_SQDIFF_NORMED"],
                "description": "匹配方法"
            },
            "threshold": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "匹配阈值"
            },
            "max_matches": {
                "type": "integer",
                "minimum": 1,
                "maximum": 100,
                "description": "最大匹配数量"
            },
            "enable_multi_scale": {
                "type": "boolean",
                "description": "启用多尺度匹配"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("template", DataType.IMAGE, True, "模板图像")
        self.add_output_connection("matches", DataType.RESULTS, "匹配结果")
        self.add_output_connection("image", DataType.IMAGE, "标记匹配的图像")
    
    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=True,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行模板匹配 - 使用装饰器优化版本"""
        # 装饰器已处理输入验证、时间记录、异常处理等
        
        image = inputs["image"]
        template = inputs["template"]
        parameters = config.parameters
        
        # 使用工具类进行模板匹配
        method = getattr(cv2, parameters.get("method", "TM_CCOEFF_NORMED"))
        threshold = parameters.get("threshold", 0.8)
        max_matches = parameters.get("max_matches", 10)
        
        # 执行模板匹配
        if parameters.get("enable_multi_scale", True):
            matches = TemplateMatchingUtils.multi_scale_template_matching(
                image, template, method, threshold, max_matches,
                scale_range=parameters.get("scale_range", [0.5, 2.0]),
                scale_step=parameters.get("scale_step", 0.1)
            )
        else:
            # 标准模板匹配
            result = cv2.matchTemplate(image, template, method)
            
            # 根据方法类型处理结果
            if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                locations = np.where(result <= threshold)
            else:
                locations = np.where(result >= threshold)
            
            matches = []
            for pt in zip(*locations[::-1]):
                confidence = result[pt[1], pt[0]]
                matches.append({
                    'x': int(pt[0]),
                    'y': int(pt[1]),
                    'width': template.shape[1],
                    'height': template.shape[0],
                    'confidence': float(confidence),
                    'scale': 1.0
                })
            
            # 限制匹配数量
            matches = sorted(matches, key=lambda x: x['confidence'], reverse=True)[:max_matches]
        
        # 在图像上绘制匹配结果
        result_image = image.copy()
        for match in matches:
            x, y, w, h = match['x'], match['y'], match['width'], match['height']
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # 绘制置信度
            confidence_text = f"{match['confidence']:.3f}"
            cv2.putText(result_image, confidence_text, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        return create_success_result(
            data={
                "matches": matches,
                "match_count": len(matches),
                "method": parameters.get("method"),
                "threshold": threshold
            },
            image=result_image,
            message=f"模板匹配完成，找到 {len(matches)} 个匹配"
        )


class CornerDetectionAlgorithm(BaseAlgorithm):
    """角点检测算法 - 使用装饰器优化版本"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "corner_detection"
    
    def get_display_name(self) -> str:
        return "角点检测"
    
    def get_description(self) -> str:
        return "检测图像中的角点特征"
    
    def get_icon_path(self) -> str:
        return "resources/icons/corner_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "harris",
            "max_corners": 100,
            "quality_level": 0.01,
            "min_distance": 10,
            "block_size": 3,
            "k": 0.04
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string", 
                "enum": ["harris", "shi_tomasi"],
                "description": "角点检测方法"
            },
            "max_corners": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最大角点数量"
            },
            "quality_level": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1.0,
                "description": "角点质量水平"
            },
            "min_distance": {
                "type": "number",
                "minimum": 1,
                "maximum": 100,
                "description": "角点间最小距离"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("corners", DataType.POINTS, "检测到的角点")
        self.add_output_connection("image", DataType.IMAGE, "标记角点的图像")
    
    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行角点检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters
        
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 使用工具类确保参数有效
        max_corners = parameters.get("max_corners", 100)
        quality_level = parameters.get("quality_level", 0.01)
        min_distance = parameters.get("min_distance", 10)
        block_size = parameters.get("block_size", 3)
        k = parameters.get("k", 0.04)
        method = parameters.get("method", "harris")
        
        # 检测角点
        if method == "harris":
            corners = cv2.goodFeaturesToTrack(
                gray, max_corners, quality_level, min_distance,
                blockSize=block_size, useHarrisDetector=True, k=k
            )
        else:  # shi_tomasi
            corners = cv2.goodFeaturesToTrack(
                gray, max_corners, quality_level, min_distance,
                blockSize=block_size, useHarrisDetector=False
            )
        
        # 处理结果
        if corners is not None:
            corners = np.float32(corners).reshape(-1, 2)
            corner_list = [{"x": float(x), "y": float(y)} for x, y in corners]
        else:
            corners = np.array([])
            corner_list = []
        
        # 在图像上绘制角点
        result_image = image.copy()
        if len(corner_list) > 0:
            for corner in corner_list:
                cv2.circle(result_image, (int(corner["x"]), int(corner["y"])), 3, (0, 255, 0), -1)
        
        return create_success_result(
            data={
                "corners": corner_list,
                "corner_count": len(corner_list),
                "method": method,
                "parameters": {
                    "max_corners": max_corners,
                    "quality_level": quality_level,
                    "min_distance": min_distance
                }
            },
            image=result_image,
            message=f"角点检测完成，找到 {len(corner_list)} 个角点"
        )


class ContourDetectionAlgorithm(BaseAlgorithm):
    """轮廓检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "contour_detection"
    
    def get_display_name(self) -> str:
        return "轮廓检测"
    
    def get_description(self) -> str:
        return "检测图像中的轮廓"
    
    def get_icon_path(self) -> str:
        return "resources/icons/contour_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "threshold_method": "binary",
            "threshold_value": 127,
            "max_value": 255,
            "retrieval_mode": "external",
            "approximation_method": "simple",
            "min_area": 100,
            "max_area": 100000,
            "min_perimeter": 0,
            "filter_by_area": True,
            "filter_by_perimeter": False
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "threshold_method": {
                "type": "string",
                "enum": ["binary", "binary_inv", "adaptive_mean", "adaptive_gaussian"],
                "description": "阈值方法"
            },
            "threshold_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "阈值"
            },
            "retrieval_mode": {
                "type": "string",
                "enum": ["external", "tree", "ccomp", "list"],
                "description": "轮廓检索模式"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("roi", DataType.ROI, False, "检测区域")
        
        self.add_output_connection("contours", DataType.CONTOURS, "检测到的轮廓")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行轮廓检测"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 二值化
            threshold_method = parameters.get("threshold_method", "binary")
            threshold_value = parameters.get("threshold_value", 127)
            max_value = parameters.get("max_value", 255)
            
            if threshold_method == "binary":
                _, binary = cv2.threshold(gray, threshold_value, max_value, cv2.THRESH_BINARY)
            elif threshold_method == "binary_inv":
                _, binary = cv2.threshold(gray, threshold_value, max_value, cv2.THRESH_BINARY_INV)
            elif threshold_method == "adaptive_mean":
                binary = cv2.adaptiveThreshold(gray, max_value, cv2.ADAPTIVE_THRESH_MEAN_C, 
                                             cv2.THRESH_BINARY, 11, 2)
            elif threshold_method == "adaptive_gaussian":
                binary = cv2.adaptiveThreshold(gray, max_value, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 11, 2)
            
            # 轮廓检测
            retrieval_mode = parameters.get("retrieval_mode", "external")
            approximation_method = parameters.get("approximation_method", "simple")
            
            mode_map = {
                "external": cv2.RETR_EXTERNAL,
                "tree": cv2.RETR_TREE,
                "ccomp": cv2.RETR_CCOMP,
                "list": cv2.RETR_LIST
            }
            
            approx_map = {
                "simple": cv2.CHAIN_APPROX_SIMPLE,
                "none": cv2.CHAIN_APPROX_NONE
            }
            
            contours, hierarchy = cv2.findContours(
                binary,
                mode_map.get(retrieval_mode, cv2.RETR_EXTERNAL),
                approx_map.get(approximation_method, cv2.CHAIN_APPROX_SIMPLE)
            )
            
            # 过滤轮廓
            filtered_contours = []
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 100000)
            min_perimeter = parameters.get("min_perimeter", 0)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)
                
                # 面积过滤
                if parameters.get("filter_by_area", True):
                    if area < min_area or area > max_area:
                        continue
                
                # 周长过滤
                if parameters.get("filter_by_perimeter", False):
                    if perimeter < min_perimeter:
                        continue
                
                filtered_contours.append(contour)
            
            # 创建结果数据
            contour_data = []
            annotated_image = image.copy()
            
            for i, contour in enumerate(filtered_contours):
                # 计算轮廓属性
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)
                
                # 边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 最小外接圆
                (center_x, center_y), radius = cv2.minEnclosingCircle(contour)
                
                contour_info = {
                    "id": i,
                    "area": float(area),
                    "perimeter": float(perimeter),
                    "bounding_rect": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "center": {"x": float(center_x), "y": float(center_y)},
                    "radius": float(radius),
                    "points": contour.tolist()
                }
                contour_data.append(contour_info)
                
                # 绘制轮廓
                cv2.drawContours(annotated_image, [contour], -1, (0, 255, 0), 2)
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
                cv2.circle(annotated_image, (int(center_x), int(center_y)), int(radius), (0, 0, 255), 1)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "contours": contour_data,
                    "contour_count": len(filtered_contours),
                    "total_found": len(contours),
                    "filtered_count": len(filtered_contours)
                },
                image=annotated_image,
                message=f"检测到 {len(filtered_contours)} 个轮廓",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"轮廓检测失败: {e}")
            return create_error_result(f"轮廓检测失败: {e}", time.time() - start_time)


# 其他检测算法的占位符实现
class BlobDetectionAlgorithm(BaseAlgorithm):
    """斑点检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "blob_detection"
    
    def get_display_name(self) -> str:
        return "斑点检测"
    
    def get_description(self) -> str:
        return "检测图像中的斑点特征"
    
    def get_icon_path(self) -> str:
        return "resources/icons/blob_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("blobs", DataType.RESULTS, "检测结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("斑点检测算法尚未实现")


class LineDetectionAlgorithm(BaseAlgorithm):
    """直线检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "line_detection"
    
    def get_display_name(self) -> str:
        return "直线检测"
    
    def get_description(self) -> str:
        return "检测图像中的直线"
    
    def get_icon_path(self) -> str:
        return "resources/icons/line_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("lines", DataType.RESULTS, "检测结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("直线检测算法尚未实现")


class CircleDetectionAlgorithm(BaseAlgorithm):
    """圆形检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "circle_detection"
    
    def get_display_name(self) -> str:
        return "圆形检测"
    
    def get_description(self) -> str:
        return "检测图像中的圆形"
    
    def get_icon_path(self) -> str:
        return "resources/icons/circle_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("circles", DataType.RESULTS, "检测结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("圆形检测算法尚未实现")


class KeyPointDetectionAlgorithm(BaseAlgorithm):
    """关键点检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "keypoint_detection"
    
    def get_display_name(self) -> str:
        return "关键点检测"
    
    def get_description(self) -> str:
        return "检测图像中的关键点特征"
    
    def get_icon_path(self) -> str:
        return "resources/icons/keypoint_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("keypoints", DataType.RESULTS, "检测结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("关键点检测算法尚未实现") 