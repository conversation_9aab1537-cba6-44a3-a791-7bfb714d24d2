"""
目标检测算法模块

提供各种目标检测算法：颜色检测、形状检测、文本检测等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)


class ColorDetectionAlgorithm(BaseAlgorithm):
    """颜色检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "color_detection"
    
    def get_display_name(self) -> str:
        return "颜色检测"
    
    def get_description(self) -> str:
        return "基于HSV颜色空间进行颜色检测"
    
    def get_icon_path(self) -> str:
        return "resources/icons/color_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "color_space": "HSV",
            "lower_bound": [0, 50, 50],
            "upper_bound": [10, 255, 255],
            "morphology_enabled": True,
            "morphology_kernel_size": 5,
            "min_area": 100,
            "max_area": 10000
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "color_space": {
                "type": "string",
                "enum": ["HSV", "RGB", "LAB"],
                "description": "颜色空间"
            },
            "lower_bound": {
                "type": "array",
                "items": {"type": "integer", "minimum": 0, "maximum": 255},
                "minItems": 3,
                "maxItems": 3,
                "description": "颜色下限值"
            },
            "upper_bound": {
                "type": "array",
                "items": {"type": "integer", "minimum": 0, "maximum": 255},
                "minItems": 3,
                "maxItems": 3,
                "description": "颜色上限值"
            },
            "morphology_enabled": {
                "type": "boolean",
                "description": "启用形态学处理"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("roi", DataType.ROI, False, "检测区域")
        
        self.add_output_connection("mask", DataType.IMAGE, "颜色掩码")
        self.add_output_connection("contours", DataType.CONTOURS, "检测到的轮廓")
        self.add_output_connection("results", DataType.RESULTS, "检测结果")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行颜色检测"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 颜色空间转换
            color_space = parameters.get("color_space", "HSV")
            if color_space == "HSV":
                converted = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            elif color_space == "LAB":
                converted = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            else:
                converted = image  # RGB/BGR
            
            # 设置颜色范围
            lower_bound = np.array(parameters.get("lower_bound", [0, 50, 50]))
            upper_bound = np.array(parameters.get("upper_bound", [10, 255, 255]))
            
            # 创建颜色掩码
            mask = cv2.inRange(converted, lower_bound, upper_bound)
            
            # 形态学处理（可选）
            if parameters.get("morphology_enabled", True):
                kernel_size = parameters.get("morphology_kernel_size", 5)
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 10000)
            
            filtered_contours = []
            detection_results = []
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    filtered_contours.append(contour)
                    
                    # 计算边界矩形
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                    else:
                        cx, cy = x + w // 2, y + h // 2
                    
                    detection_results.append({
                        "area": float(area),
                        "center": {"x": cx, "y": cy},
                        "bounding_rect": {"x": x, "y": y, "width": w, "height": h},
                        "perimeter": float(cv2.arcLength(contour, True))
                    })
            
            # 创建标注图像
            annotated_image = image.copy()
            for i, contour in enumerate(filtered_contours):
                # 绘制轮廓
                cv2.drawContours(annotated_image, [contour], -1, (0, 255, 0), 2)
                
                # 绘制边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (255, 0, 0), 2)
                
                # 标注序号
                cv2.putText(annotated_image, str(i), (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "mask": mask,
                    "contours": [contour.tolist() for contour in filtered_contours],
                    "results": detection_results,
                    "detection_count": len(filtered_contours),
                    "color_space": color_space,
                    "bounds": {"lower": lower_bound.tolist(), "upper": upper_bound.tolist()}
                },
                image=annotated_image,
                message=f"检测到 {len(filtered_contours)} 个颜色目标",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"颜色检测失败: {e}")
            return create_error_result(f"颜色检测失败: {e}", time.time() - start_time)


class ShapeDetectionAlgorithm(BaseAlgorithm):
    """形状检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "shape_detection"
    
    def get_display_name(self) -> str:
        return "形状检测"
    
    def get_description(self) -> str:
        return "检测基本几何形状：圆形、矩形、三角形等"
    
    def get_icon_path(self) -> str:
        return "resources/icons/shape_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "shapes_to_detect": ["circle", "rectangle", "triangle"],
            "min_area": 100,
            "max_area": 10000,
            "approximation_accuracy": 0.02,
            "circularity_threshold": 0.7,
            "rectangularity_threshold": 0.85
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "shapes_to_detect": {
                "type": "array",
                "items": {
                    "type": "string",
                    "enum": ["circle", "rectangle", "triangle", "polygon"]
                },
                "description": "要检测的形状类型"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            },
            "approximation_accuracy": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 0.1,
                "description": "轮廓近似精度"
            },
            "circularity_threshold": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 1.0,
                "description": "圆形判定阈值"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "输入轮廓")
        
        self.add_output_connection("shapes", DataType.RESULTS, "检测到的形状")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def _classify_shape(self, contour: np.ndarray, parameters: Dict[str, Any]) -> str:
        """分类形状"""
        # 计算轮廓近似
        epsilon = parameters.get("approximation_accuracy", 0.02) * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 根据顶点数判断形状
        vertex_count = len(approx)
        
        if vertex_count == 3:
            return "triangle"
        elif vertex_count == 4:
            # 进一步判断是否为矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h
            
            # 计算轮廓面积与边界矩形面积的比例
            contour_area = cv2.contourArea(contour)
            rect_area = w * h
            rectangularity = contour_area / rect_area if rect_area > 0 else 0
            
            if rectangularity > parameters.get("rectangularity_threshold", 0.85):
                if 0.8 <= aspect_ratio <= 1.2:
                    return "square"
                else:
                    return "rectangle"
            else:
                return "quadrilateral"
        else:
            # 判断是否为圆形
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > parameters.get("circularity_threshold", 0.7):
                    return "circle"
            
            if vertex_count > 4:
                return "polygon"
            else:
                return "unknown"
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行形状检测"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 获取轮廓
            if "contours" in inputs and inputs["contours"]:
                # 使用提供的轮廓
                contours = [np.array(contour, dtype=np.int32) for contour in inputs["contours"]]
            else:
                # 从图像中检测轮廓
                if len(image.shape) == 3:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = image
                
                # 二值化
                _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 10000)
            shapes_to_detect = parameters.get("shapes_to_detect", ["circle", "rectangle", "triangle"])
            
            detected_shapes = []
            annotated_image = image.copy()
            
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area < min_area or area > max_area:
                    continue
                
                # 分类形状
                shape_type = self._classify_shape(contour, parameters)
                
                if shape_type in shapes_to_detect or "all" in shapes_to_detect:
                    # 计算几何属性
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                    else:
                        cx, cy = x + w // 2, y + h // 2
                    
                    shape_info = {
                        "id": i,
                        "type": shape_type,
                        "area": float(area),
                        "perimeter": float(cv2.arcLength(contour, True)),
                        "center": {"x": cx, "y": cy},
                        "bounding_rect": {"x": x, "y": y, "width": w, "height": h}
                    }
                    
                    # 添加特定形状的属性
                    if shape_type == "circle":
                        (center_x, center_y), radius = cv2.minEnclosingCircle(contour)
                        shape_info["circle"] = {
                            "center": {"x": float(center_x), "y": float(center_y)},
                            "radius": float(radius)
                        }
                    
                    detected_shapes.append(shape_info)
                    
                    # 绘制形状
                    color = self._get_shape_color(shape_type)
                    cv2.drawContours(annotated_image, [contour], -1, color, 2)
                    
                    # 标注形状类型
                    cv2.putText(annotated_image, f"{shape_type}_{i}", (x, y - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "shapes": detected_shapes,
                    "shape_count": len(detected_shapes),
                    "shape_types": list(set(shape["type"] for shape in detected_shapes))
                },
                image=annotated_image,
                message=f"检测到 {len(detected_shapes)} 个形状",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"形状检测失败: {e}")
            return create_error_result(f"形状检测失败: {e}", time.time() - start_time)
    
    def _get_shape_color(self, shape_type: str) -> Tuple[int, int, int]:
        """获取形状对应的颜色"""
        color_map = {
            "circle": (0, 255, 0),      # 绿色
            "rectangle": (255, 0, 0),   # 蓝色
            "square": (0, 0, 255),      # 红色
            "triangle": (255, 255, 0),  # 青色
            "polygon": (255, 0, 255),   # 紫色
            "unknown": (128, 128, 128)  # 灰色
        }
        return color_map.get(shape_type, (255, 255, 255))


# 其他目标检测算法的占位符实现
class TextDetectionAlgorithm(BaseAlgorithm):
    """文本检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "text_detection"
    
    def get_display_name(self) -> str:
        return "文本检测"
    
    def get_description(self) -> str:
        return "检测图像中的文本区域"
    
    def get_icon_path(self) -> str:
        return "resources/icons/text_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("text_regions", DataType.RESULTS, "文本区域")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("文本检测算法尚未实现")


class BarcodeDetectionAlgorithm(BaseAlgorithm):
    """条码检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "barcode_detection"
    
    def get_display_name(self) -> str:
        return "条码检测"
    
    def get_description(self) -> str:
        return "检测和识别一维/二维条码"
    
    def get_icon_path(self) -> str:
        return "resources/icons/barcode_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("barcodes", DataType.RESULTS, "条码信息")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("条码检测算法尚未实现")


class FaceDetectionAlgorithm(BaseAlgorithm):
    """人脸检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION
    
    def get_algorithm_name(self) -> str:
        return "face_detection"
    
    def get_display_name(self) -> str:
        return "人脸检测"
    
    def get_description(self) -> str:
        return "检测图像中的人脸"
    
    def get_icon_path(self) -> str:
        return "resources/icons/face_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("faces", DataType.RESULTS, "人脸信息")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("人脸检测算法尚未实现") 