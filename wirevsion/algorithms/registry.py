"""
算法注册表模块

提供算法的注册、发现和实例化功能
"""

from typing import Dict, Type, List, Optional, Any
from functools import wraps
from loguru import logger

from .base_algorithm import BaseAlgorithm, AlgorithmType


class AlgorithmRegistry:
    """算法注册表"""
    
    def __init__(self):
        """初始化注册表"""
        self._algorithms: Dict[str, Dict[str, Type[BaseAlgorithm]]] = {}
        self._algorithm_metadata: Dict[str, Dict[str, Dict[str, Any]]] = {}
        
        # 初始化所有算法类型
        for algorithm_type in AlgorithmType:
            self._algorithms[algorithm_type.value] = {}
            self._algorithm_metadata[algorithm_type.value] = {}
        
        logger.debug("算法注册表初始化完成")
    
    def register(self, category: str, algorithm_name: str, 
                algorithm_class: Type[BaseAlgorithm], metadata: Dict[str, Any] = None):
        """
        注册算法
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            algorithm_class: 算法类
            metadata: 元数据
        """
        if category not in self._algorithms:
            self._algorithms[category] = {}
            self._algorithm_metadata[category] = {}
        
        self._algorithms[category][algorithm_name] = algorithm_class
        
        # 存储元数据
        if metadata is None:
            metadata = {}
        
        # 从算法类中提取元数据
        try:
            temp_instance = algorithm_class()
            metadata.update({
                'display_name': temp_instance.get_display_name(),
                'description': temp_instance.get_description(),
                'icon_path': temp_instance.get_icon_path(),
                'algorithm_type': temp_instance.get_algorithm_type().value,
                'input_connections': [
                    {
                        'name': conn.name,
                        'data_type': conn.data_type.value,
                        'required': conn.required,
                        'description': conn.description
                    }
                    for conn in temp_instance.get_input_connections()
                ],
                'output_connections': [
                    {
                        'name': conn.name,
                        'data_type': conn.data_type.value,
                        'description': conn.description
                    }
                    for conn in temp_instance.get_output_connections()
                ],
                'parameter_schema': temp_instance.get_parameter_schema(),
                'default_parameters': temp_instance.get_default_parameters()
            })
        except Exception as e:
            logger.warning(f"无法从算法类 {algorithm_class.__name__} 提取元数据: {e}")
        
        self._algorithm_metadata[category][algorithm_name] = metadata
        
        logger.debug(f"注册算法: {category}.{algorithm_name}")
    
    def get_algorithm_class(self, category: str, algorithm_name: str) -> Optional[Type[BaseAlgorithm]]:
        """
        获取算法类
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            
        Returns:
            算法类
        """
        return self._algorithms.get(category, {}).get(algorithm_name)
    
    def create_algorithm(self, category: str, algorithm_name: str) -> Optional[BaseAlgorithm]:
        """
        创建算法实例
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            
        Returns:
            算法实例
        """
        algorithm_class = self.get_algorithm_class(category, algorithm_name)
        if algorithm_class:
            try:
                return algorithm_class()
            except Exception as e:
                logger.error(f"创建算法实例失败 {category}.{algorithm_name}: {e}")
                return None
        return None
    
    def get_algorithms_by_category(self, category: str) -> Dict[str, Type[BaseAlgorithm]]:
        """
        获取指定类别的所有算法
        
        Args:
            category: 算法类别
            
        Returns:
            算法字典
        """
        return self._algorithms.get(category, {})
    
    def get_all_categories(self) -> List[str]:
        """
        获取所有算法类别
        
        Returns:
            类别列表
        """
        return list(self._algorithms.keys())
    
    def get_algorithm_metadata(self, category: str, algorithm_name: str) -> Optional[Dict[str, Any]]:
        """
        获取算法元数据
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            
        Returns:
            元数据
        """
        return self._algorithm_metadata.get(category, {}).get(algorithm_name)
    
    def get_category_metadata(self, category: str) -> Dict[str, Dict[str, Any]]:
        """
        获取指定类别的所有算法元数据
        
        Args:
            category: 算法类别
            
        Returns:
            元数据字典
        """
        return self._algorithm_metadata.get(category, {})
    
    def get_all_algorithms(self) -> Dict[str, Dict[str, Type[BaseAlgorithm]]]:
        """
        获取所有算法
        
        Returns:
            所有算法
        """
        return self._algorithms
    
    def get_all_metadata(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        获取所有元数据
        
        Returns:
            所有元数据
        """
        return self._algorithm_metadata
    
    def search_algorithms(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索算法
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的算法列表
        """
        results = []
        keyword_lower = keyword.lower()
        
        for category, algorithms in self._algorithm_metadata.items():
            for algorithm_name, metadata in algorithms.items():
                # 搜索名称、显示名称和描述
                if (keyword_lower in algorithm_name.lower() or
                    keyword_lower in metadata.get('display_name', '').lower() or
                    keyword_lower in metadata.get('description', '').lower()):
                    
                    result = {
                        'category': category,
                        'algorithm_name': algorithm_name,
                        'metadata': metadata
                    }
                    results.append(result)
        
        return results
    
    def is_registered(self, category: str, algorithm_name: str) -> bool:
        """
        检查算法是否已注册
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            
        Returns:
            是否已注册
        """
        return (category in self._algorithms and 
                algorithm_name in self._algorithms[category])
    
    def unregister(self, category: str, algorithm_name: str) -> bool:
        """
        取消注册算法
        
        Args:
            category: 算法类别
            algorithm_name: 算法名称
            
        Returns:
            是否成功
        """
        if self.is_registered(category, algorithm_name):
            del self._algorithms[category][algorithm_name]
            del self._algorithm_metadata[category][algorithm_name]
            logger.debug(f"取消注册算法: {category}.{algorithm_name}")
            return True
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取注册表统计信息
        
        Returns:
            统计信息
        """
        stats = {
            'total_algorithms': 0,
            'categories': {},
            'total_categories': len(self._algorithms)
        }
        
        for category, algorithms in self._algorithms.items():
            count = len(algorithms)
            stats['categories'][category] = count
            stats['total_algorithms'] += count
        
        return stats
    
    def validate_registry(self) -> Dict[str, Any]:
        """
        验证注册表
        
        Returns:
            验证结果
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        for category, algorithms in self._algorithms.items():
            for algorithm_name, algorithm_class in algorithms.items():
                try:
                    # 尝试创建实例
                    instance = algorithm_class()
                    
                    # 验证必需方法
                    required_methods = [
                        'get_algorithm_type', 'get_algorithm_name',
                        'get_display_name', 'get_description',
                        'get_icon_path', 'get_default_parameters',
                        'get_parameter_schema', 'execute'
                    ]
                    
                    for method_name in required_methods:
                        if not hasattr(instance, method_name):
                            validation_result['errors'].append(
                                f"{category}.{algorithm_name} 缺少方法: {method_name}"
                            )
                            validation_result['valid'] = False
                    
                    # 验证类型一致性
                    if hasattr(instance, 'get_algorithm_type'):
                        algorithm_type = instance.get_algorithm_type()
                        if algorithm_type.value != category:
                            validation_result['warnings'].append(
                                f"{category}.{algorithm_name} 类型不匹配: "
                                f"预期 {category}, 实际 {algorithm_type.value}"
                            )
                
                except Exception as e:
                    validation_result['errors'].append(
                        f"{category}.{algorithm_name} 实例化失败: {e}"
                    )
                    validation_result['valid'] = False
        
        return validation_result


def register_algorithm(category: str, algorithm_name: str, metadata: Dict[str, Any] = None):
    """
    算法注册装饰器
    
    Args:
        category: 算法类别
        algorithm_name: 算法名称
        metadata: 元数据
    """
    def decorator(algorithm_class: Type[BaseAlgorithm]):
        # 这里应该有一个全局注册表实例
        # 由于循环导入问题，在实际使用时需要延迟注册
        algorithm_class._registry_info = {
            'category': category,
            'algorithm_name': algorithm_name,
            'metadata': metadata or {}
        }
        return algorithm_class
    
    return decorator


def get_registry() -> AlgorithmRegistry:
    """
    获取全局算法注册表实例
    
    Returns:
        注册表实例
    """
    # 这里返回全局实例，在 __init__.py 中创建
    from . import algorithm_registry
    return algorithm_registry 