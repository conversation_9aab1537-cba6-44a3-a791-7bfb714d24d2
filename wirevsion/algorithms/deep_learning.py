"""
深度学习算法模块

提供各种深度学习算法：YOLO检测、图像分类、语义分割、姿态估计等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)


class YOLODetectionAlgorithm(BaseAlgorithm):
    """YOLO目标检测算法"""
    
    def __init__(self):
        super().__init__()
        self.model = None
        self.model_loaded = False
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.DEEP_LEARNING
    
    def get_algorithm_name(self) -> str:
        return "yolo_detection"
    
    def get_display_name(self) -> str:
        return "YOLO检测"
    
    def get_description(self) -> str:
        return "使用YOLO模型进行目标检测"
    
    def get_icon_path(self) -> str:
        return "resources/icons/yolo_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "model_path": "yolov8n.pt",
            "confidence_threshold": 0.5,
            "iou_threshold": 0.45,
            "max_detections": 100,
            "input_size": [640, 640],
            "class_filter": [],  # 空列表表示检测所有类别
            "device": "auto"  # auto, cpu, cuda, mps
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "model_path": {
                "type": "string",
                "description": "YOLO模型文件路径"
            },
            "confidence_threshold": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "置信度阈值"
            },
            "iou_threshold": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "IoU阈值"
            },
            "max_detections": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最大检测数量"
            },
            "input_size": {
                "type": "array",
                "items": {"type": "integer"},
                "minItems": 2,
                "maxItems": 2,
                "description": "输入图像尺寸 [width, height]"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("roi", DataType.ROI, False, "检测区域")
        
        self.add_output_connection("detections", DataType.RESULTS, "检测结果")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
        self.add_output_connection("bboxes", DataType.RESULTS, "边界框")
        self.add_output_connection("classes", DataType.RESULTS, "类别信息")
    
    def _load_model(self, model_path: str, device: str = "auto") -> bool:
        """加载YOLO模型"""
        try:
            # 这里使用ultralytics YOLO
            from ultralytics import YOLO
            
            self.model = YOLO(model_path)
            
            # 设置设备
            if device == "auto":
                # 自动选择设备
                import torch
                if torch.cuda.is_available():
                    device = "cuda"
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    device = "mps"
                else:
                    device = "cpu"
            
            self.model.to(device)
            self.model_loaded = True
            
            logger.info(f"YOLO模型加载成功: {model_path}, 设备: {device}")
            return True
            
        except Exception as e:
            logger.error(f"YOLO模型加载失败: {e}")
            return False
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行YOLO检测"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 加载模型（如果未加载）
            model_path = parameters.get("model_path", "yolov8n.pt")
            device = parameters.get("device", "auto")
            
            if not self.model_loaded:
                if not self._load_model(model_path, device):
                    return create_error_result("模型加载失败", time.time() - start_time)
            
            # 检测参数
            confidence_threshold = parameters.get("confidence_threshold", 0.5)
            iou_threshold = parameters.get("iou_threshold", 0.45)
            max_detections = parameters.get("max_detections", 100)
            input_size = parameters.get("input_size", [640, 640])
            class_filter = parameters.get("class_filter", [])
            
            # 执行检测
            results = self.model(
                image,
                conf=confidence_threshold,
                iou=iou_threshold,
                imgsz=input_size,
                max_det=max_detections,
                verbose=False
            )
            
            # 解析结果
            detections = []
            annotated_image = image.copy()
            
            if results and len(results) > 0:
                result = results[0]  # 取第一个结果
                
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()  # 边界框
                    confidences = result.boxes.conf.cpu().numpy()  # 置信度
                    classes = result.boxes.cls.cpu().numpy()  # 类别
                    
                    # 获取类别名称
                    class_names = self.model.names
                    
                    for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                        class_id = int(cls)
                        class_name = class_names.get(class_id, f"class_{class_id}")
                        
                        # 应用类别过滤
                        if class_filter and class_name not in class_filter:
                            continue
                        
                        x1, y1, x2, y2 = map(int, box)
                        width = x2 - x1
                        height = y2 - y1
                        center_x = x1 + width // 2
                        center_y = y1 + height // 2
                        
                        detection = {
                            "id": i,
                            "class_id": class_id,
                            "class_name": class_name,
                            "confidence": float(conf),
                            "bbox": {
                                "x1": x1, "y1": y1, "x2": x2, "y2": y2,
                                "width": width, "height": height,
                                "center_x": center_x, "center_y": center_y
                            }
                        }
                        detections.append(detection)
                        
                        # 绘制边界框
                        color = self._get_class_color(class_id)
                        cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color, 2)
                        
                        # 绘制标签
                        label = f"{class_name}: {conf:.2f}"
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                        cv2.rectangle(annotated_image, (x1, y1 - label_size[1] - 10),
                                     (x1 + label_size[0], y1), color, -1)
                        cv2.putText(annotated_image, label, (x1, y1 - 5),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 统计信息
            class_counts = {}
            for det in detections:
                class_name = det["class_name"]
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "detections": detections,
                    "detection_count": len(detections),
                    "class_counts": class_counts,
                    "model_info": {
                        "model_path": model_path,
                        "input_size": input_size,
                        "device": device
                    }
                },
                image=annotated_image,
                message=f"检测到 {len(detections)} 个目标",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {e}")
            return create_error_result(f"YOLO检测失败: {e}", time.time() - start_time)
    
    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """获取类别对应的颜色"""
        # 使用固定的颜色映射
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
            (255, 0, 255), (0, 255, 255), (128, 0, 0), (0, 128, 0),
            (0, 0, 128), (128, 128, 0), (128, 0, 128), (0, 128, 128),
            (192, 192, 192), (128, 128, 128), (255, 165, 0), (255, 20, 147)
        ]
        return colors[class_id % len(colors)]


class ClassificationAlgorithm(BaseAlgorithm):
    """图像分类算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.DEEP_LEARNING
    
    def get_algorithm_name(self) -> str:
        return "classification"
    
    def get_display_name(self) -> str:
        return "图像分类"
    
    def get_description(self) -> str:
        return "使用深度学习模型进行图像分类"
    
    def get_icon_path(self) -> str:
        return "resources/icons/classification.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "model_path": "",
            "input_size": [224, 224],
            "top_k": 5,
            "confidence_threshold": 0.1,
            "preprocessing": {
                "normalize": True,
                "mean": [0.485, 0.456, 0.406],
                "std": [0.229, 0.224, 0.225]
            }
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "model_path": {
                "type": "string",
                "description": "分类模型文件路径"
            },
            "input_size": {
                "type": "array",
                "items": {"type": "integer"},
                "description": "输入图像尺寸"
            },
            "top_k": {
                "type": "integer",
                "minimum": 1,
                "maximum": 20,
                "description": "返回前K个预测结果"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("classifications", DataType.RESULTS, "分类结果")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行图像分类"""
        return create_error_result("图像分类算法尚未实现")


class SegmentationAlgorithm(BaseAlgorithm):
    """语义分割算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.DEEP_LEARNING
    
    def get_algorithm_name(self) -> str:
        return "segmentation"
    
    def get_display_name(self) -> str:
        return "语义分割"
    
    def get_description(self) -> str:
        return "使用深度学习模型进行语义分割"
    
    def get_icon_path(self) -> str:
        return "resources/icons/segmentation.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "model_path": "",
            "input_size": [512, 512],
            "num_classes": 21,
            "confidence_threshold": 0.5
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "model_path": {
                "type": "string",
                "description": "分割模型文件路径"
            },
            "input_size": {
                "type": "array",
                "items": {"type": "integer"},
                "description": "输入图像尺寸"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("segmentation_mask", DataType.IMAGE, "分割掩码")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行语义分割"""
        return create_error_result("语义分割算法尚未实现")


class PoseEstimationAlgorithm(BaseAlgorithm):
    """姿态估计算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.DEEP_LEARNING
    
    def get_algorithm_name(self) -> str:
        return "pose_estimation"
    
    def get_display_name(self) -> str:
        return "姿态估计"
    
    def get_description(self) -> str:
        return "使用深度学习模型进行人体姿态估计"
    
    def get_icon_path(self) -> str:
        return "resources/icons/pose_estimation.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "model_path": "",
            "input_size": [256, 256],
            "confidence_threshold": 0.3,
            "keypoint_threshold": 0.2
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "model_path": {
                "type": "string",
                "description": "姿态估计模型文件路径"
            },
            "confidence_threshold": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "置信度阈值"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("poses", DataType.RESULTS, "姿态信息")
        self.add_output_connection("keypoints", DataType.POINTS, "关键点")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行姿态估计"""
        return create_error_result("姿态估计算法尚未实现") 