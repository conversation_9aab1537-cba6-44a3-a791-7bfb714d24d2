#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像处理算法模块

提供各种基础图像处理算法：高斯模糊、形态学操作、阈值处理等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils


class GaussianBlurAlgorithm(BaseAlgorithm):
    """高斯模糊算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "gaussian_blur"
    
    def get_display_name(self) -> str:
        return "高斯模糊"
    
    def get_description(self) -> str:
        return "对图像进行高斯模糊处理"
    
    def get_icon_path(self) -> str:
        return "resources/icons/gaussian_blur.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "kernel_size_x": 15,
            "kernel_size_y": 15,
            "sigma_x": 0,
            "sigma_y": 0
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "kernel_size_x": {
                "type": "integer",
                "minimum": 1,
                "maximum": 99,
                "description": "X方向核大小（必须为奇数）"
            },
            "kernel_size_y": {
                "type": "integer", 
                "minimum": 1,
                "maximum": 99,
                "description": "Y方向核大小（必须为奇数）"
            },
            "sigma_x": {
                "type": "number",
                "minimum": 0,
                "maximum": 100,
                "description": "X方向标准差"
            },
            "sigma_y": {
                "type": "number",
                "minimum": 0,
                "maximum": 100,
                "description": "Y方向标准差"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "模糊处理后的图像")
    
    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行高斯模糊 - 使用装饰器优化版本"""
        # 装饰器已经处理了输入验证、时间记录、异常处理等
        # 这里只需要专注于核心算法逻辑
        
        image = inputs["image"]
        parameters = config.parameters
        
        # 使用工具类确保核大小为奇数
        ksize_x = parameters.get("kernel_size_x", 15)
        ksize_y = parameters.get("kernel_size_y", 15)
        if ksize_x % 2 == 0:
            ksize_x += 1
        if ksize_y % 2 == 0:
            ksize_y += 1
        
        sigma_x = parameters.get("sigma_x", 0)
        sigma_y = parameters.get("sigma_y", 0)
        
        # 执行高斯模糊
        blurred = cv2.GaussianBlur(image, (ksize_x, ksize_y), sigma_x, sigmaY=sigma_y)
        
        return create_success_result(
            data={
                "kernel_size": (ksize_x, ksize_y),
                "sigma": (sigma_x, sigma_y)
            },
            image=blurred,
            message=f"高斯模糊完成，核大小: ({ksize_x}, {ksize_y})"
        )


class MedianBlurAlgorithm(BaseAlgorithm):
    """中值滤波算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "median_blur"
    
    def get_display_name(self) -> str:
        return "中值滤波"
    
    def get_description(self) -> str:
        return "对图像进行中值滤波处理，用于去除椒盐噪声"
    
    def get_icon_path(self) -> str:
        return "resources/icons/median_blur.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "kernel_size": 5
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "kernel_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 99,
                "description": "核大小（必须为奇数）"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "滤波处理后的图像")
    
    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行中值滤波 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters
        
        ksize = parameters.get("kernel_size", 5)
        if ksize % 2 == 0:
            ksize += 1
        
        # 执行中值滤波
        filtered = cv2.medianBlur(image, ksize)
        
        return create_success_result(
            data={"kernel_size": ksize},
            image=filtered,
            message=f"中值滤波完成，核大小: {ksize}"
        )


class ThresholdAlgorithm(BaseAlgorithm):
    """阈值处理算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "threshold"
    
    def get_display_name(self) -> str:
        return "阈值处理"
    
    def get_description(self) -> str:
        return "对图像进行二值化阈值处理"
    
    def get_icon_path(self) -> str:
        return "resources/icons/threshold.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "threshold_value": 127,
            "max_value": 255,
            "threshold_type": "binary",
            "use_otsu": False,
            "use_triangle": False
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "threshold_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "阈值"
            },
            "max_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "最大值"
            },
            "threshold_type": {
                "type": "string",
                "enum": ["binary", "binary_inv", "trunc", "tozero", "tozero_inv"],
                "description": "阈值类型"
            },
            "use_otsu": {
                "type": "boolean",
                "description": "使用Otsu自动阈值"
            },
            "use_triangle": {
                "type": "boolean", 
                "description": "使用Triangle自动阈值"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "二值化图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行阈值处理"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            threshold_value = parameters.get("threshold_value", 127)
            max_value = parameters.get("max_value", 255)
            threshold_type = parameters.get("threshold_type", "binary")
            
            # 设置阈值类型
            type_map = {
                "binary": cv2.THRESH_BINARY,
                "binary_inv": cv2.THRESH_BINARY_INV,
                "trunc": cv2.THRESH_TRUNC,
                "tozero": cv2.THRESH_TOZERO,
                "tozero_inv": cv2.THRESH_TOZERO_INV
            }
            
            thresh_type = type_map.get(threshold_type, cv2.THRESH_BINARY)
            
            # 添加自动阈值选择
            if parameters.get("use_otsu", False):
                thresh_type |= cv2.THRESH_OTSU
            elif parameters.get("use_triangle", False):
                thresh_type |= cv2.THRESH_TRIANGLE
            
            # 执行阈值处理
            ret_val, thresholded = cv2.threshold(gray, threshold_value, max_value, thresh_type)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "threshold_value": ret_val,
                    "actual_threshold": ret_val,
                    "threshold_type": threshold_type
                },
                image=thresholded,
                message=f"阈值处理完成，阈值: {ret_val:.1f}",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"阈值处理失败: {e}")
            return create_error_result(f"阈值处理失败: {e}", time.time() - start_time)


class EdgeDetectionAlgorithm(BaseAlgorithm):
    """边缘检测算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "edge_detection"
    
    def get_display_name(self) -> str:
        return "边缘检测"
    
    def get_description(self) -> str:
        return "使用Canny算法检测图像边缘"
    
    def get_icon_path(self) -> str:
        return "resources/icons/edge_detection.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "low_threshold": 50,
            "high_threshold": 150,
            "aperture_size": 3,
            "l2_gradient": False
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "low_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 500,
                "description": "低阈值"
            },
            "high_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 500,
                "description": "高阈值"
            },
            "aperture_size": {
                "type": "integer",
                "enum": [3, 5, 7],
                "description": "Sobel核大小"
            },
            "l2_gradient": {
                "type": "boolean",
                "description": "使用L2梯度"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("edges", DataType.IMAGE, "边缘检测结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行边缘检测"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            low_threshold = parameters.get("low_threshold", 50)
            high_threshold = parameters.get("high_threshold", 150)
            aperture_size = parameters.get("aperture_size", 3)
            l2_gradient = parameters.get("l2_gradient", False)
            
            # 执行Canny边缘检测
            edges = cv2.Canny(gray, low_threshold, high_threshold, 
                             apertureSize=aperture_size, L2gradient=l2_gradient)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "low_threshold": low_threshold,
                    "high_threshold": high_threshold,
                    "edge_count": np.count_nonzero(edges)
                },
                image=edges,
                message=f"边缘检测完成，检测到 {np.count_nonzero(edges)} 个边缘像素",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"边缘检测失败: {e}")
            return create_error_result(f"边缘检测失败: {e}", time.time() - start_time)


class MorphologyAlgorithm(BaseAlgorithm):
    """形态学操作算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "morphology"
    
    def get_display_name(self) -> str:
        return "形态学操作"
    
    def get_description(self) -> str:
        return "对图像进行形态学操作：腐蚀、膨胀、开运算、闭运算等"
    
    def get_icon_path(self) -> str:
        return "resources/icons/morphology.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "operation": "opening",
            "kernel_shape": "rectangle",
            "kernel_size": 5,
            "iterations": 1
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "operation": {
                "type": "string",
                "enum": ["erosion", "dilation", "opening", "closing", "gradient", "tophat", "blackhat"],
                "description": "形态学操作类型"
            },
            "kernel_shape": {
                "type": "string",
                "enum": ["rectangle", "ellipse", "cross"],
                "description": "核形状"
            },
            "kernel_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 25,
                "description": "核大小"
            },
            "iterations": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "迭代次数"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "形态学处理后的图像")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行形态学操作"""
        start_time = time.time()
        
        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)
            
            image = inputs["image"]
            parameters = config.parameters
            
            operation = parameters.get("operation", "opening")
            kernel_shape = parameters.get("kernel_shape", "rectangle")
            kernel_size = parameters.get("kernel_size", 5)
            iterations = parameters.get("iterations", 1)
            
            # 创建结构元素
            shape_map = {
                "rectangle": cv2.MORPH_RECT,
                "ellipse": cv2.MORPH_ELLIPSE,
                "cross": cv2.MORPH_CROSS
            }
            
            kernel = cv2.getStructuringElement(
                shape_map.get(kernel_shape, cv2.MORPH_RECT),
                (kernel_size, kernel_size)
            )
            
            # 执行形态学操作
            op_map = {
                "erosion": cv2.MORPH_ERODE,
                "dilation": cv2.MORPH_DILATE,
                "opening": cv2.MORPH_OPEN,
                "closing": cv2.MORPH_CLOSE,
                "gradient": cv2.MORPH_GRADIENT,
                "tophat": cv2.MORPH_TOPHAT,
                "blackhat": cv2.MORPH_BLACKHAT
            }
            
            if operation in ["erosion", "dilation"]:
                if operation == "erosion":
                    result = cv2.erode(image, kernel, iterations=iterations)
                else:
                    result = cv2.dilate(image, kernel, iterations=iterations)
            else:
                result = cv2.morphologyEx(image, op_map[operation], kernel, iterations=iterations)
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "operation": operation,
                    "kernel_shape": kernel_shape,
                    "kernel_size": kernel_size,
                    "iterations": iterations
                },
                image=result,
                message=f"形态学{operation}操作完成",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"形态学操作失败: {e}")
            return create_error_result(f"形态学操作失败: {e}", time.time() - start_time)


# 其他图像处理算法的占位符实现
class BilateralFilterAlgorithm(BaseAlgorithm):
    """双边滤波算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "bilateral_filter"
    
    def get_display_name(self) -> str:
        return "双边滤波"
    
    def get_description(self) -> str:
        return "双边滤波，保持边缘的同时去噪"
    
    def get_icon_path(self) -> str:
        return "resources/icons/bilateral_filter.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "滤波结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("双边滤波算法尚未实现")


class ColorSpaceAlgorithm(BaseAlgorithm):
    """颜色空间转换算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "color_space"
    
    def get_display_name(self) -> str:
        return "颜色空间转换"
    
    def get_description(self) -> str:
        return "在不同颜色空间之间转换"
    
    def get_icon_path(self) -> str:
        return "resources/icons/color_space.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "转换结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("颜色空间转换算法尚未实现")


class HistogramAlgorithm(BaseAlgorithm):
    """直方图算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "histogram"
    
    def get_display_name(self) -> str:
        return "直方图处理"
    
    def get_description(self) -> str:
        return "计算和处理图像直方图"
    
    def get_icon_path(self) -> str:
        return "resources/icons/histogram.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("histogram", DataType.RESULTS, "直方图数据")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("直方图算法尚未实现")


class ContrastAlgorithm(BaseAlgorithm):
    """对比度调整算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "contrast"
    
    def get_display_name(self) -> str:
        return "对比度调整"
    
    def get_description(self) -> str:
        return "调整图像对比度和亮度"
    
    def get_icon_path(self) -> str:
        return "resources/icons/contrast.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "调整结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("对比度调整算法尚未实现")


class NoiseReductionAlgorithm(BaseAlgorithm):
    """降噪算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING
    
    def get_algorithm_name(self) -> str:
        return "noise_reduction"
    
    def get_display_name(self) -> str:
        return "降噪处理"
    
    def get_description(self) -> str:
        return "对图像进行降噪处理"
    
    def get_icon_path(self) -> str:
        return "resources/icons/noise_reduction.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {}
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {}
    
    def _setup_connections(self):
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "降噪结果")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        return create_error_result("降噪算法尚未实现") 