# WireVision - 智能视觉检测系统

## 项目简介

WireVision 是一个基于 PyQt5 和 OpenCV 的智能视觉检测系统，提供了现代化的用户界面和强大的图像处理功能。

## 主要特性

- 🎨 **现代化UI设计**：深色主题，无边框窗口，流畅动画
- 📷 **相机管理**：实时预览，参数调整，多相机支持
- 🔧 **工作流编辑器**：可视化算法流程设计，节点拖放，连线编辑
- 🤖 **丰富的算法库**：图像滤波、边缘检测、形态学操作、深度学习等
- 📊 **实时处理**：高性能图像处理，结果可视化

## 快速开始

### 环境要求

- Python 3.8+
- Poetry（依赖管理）

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/yourusername/WireVision.git
cd WireVision
```

2. 安装依赖
```bash
poetry install
```

3. 运行程序
```bash
poetry run python main.py
```

## 工作流编辑器使用说明

### 节点操作
- **添加节点**：从左侧节点库拖拽算法节点到画布
- **移动节点**：直接拖拽节点到新位置
- **连接节点**：从任意端口拖拽到另一个节点的端口（每个节点有4个通用端口）
- **删除节点**：选中节点后按 Delete 键

### 端口说明
每个节点都有4个通用端口：
- **上端口**：顶部中心
- **下端口**：底部中心
- **左端口**：左侧中心
- **右端口**：右侧中心

所有端口都是通用的，可以自由连接。

### 画布操作
- **平移画布**：按住鼠标中键拖拽
- **缩放画布**：使用鼠标滚轮
- **框选节点**：左键拖拽空白区域

## 算法库

系统内置了丰富的图像处理算法：

### 输入/输出
- 相机输入、图像输入、视频输入
- 结果输出、图像输出

### 图像处理
- **滤波**：高斯模糊、中值滤波、双边滤波
- **边缘检测**：Canny、Sobel、Laplacian
- **形态学**：腐蚀、膨胀、开运算、闭运算
- **特征检测**：轮廓、角点、模板匹配、颜色检测
- **变换**：缩放、旋转、透视变换

### 深度学习
- YOLO目标检测
- 图像分类
- 语义分割

## 项目结构

```
WireVision/
├── main.py              # 程序入口
├── wirevsion/           # 核心代码
│   ├── ui/              # 用户界面
│   ├── core/            # 核心算法
│   ├── camera/          # 相机管理
│   └── utils/           # 工具函数
├── configs/             # 配置文件
├── resources/           # 资源文件
└── tests/               # 测试代码
```

## 开发指南

### 添加新算法

1. 在 `wirevsion/algorithms/` 创建算法类
2. 在节点库中注册新算法
3. 实现算法的处理逻辑

### 自定义主题

编辑 `wirevsion/ui/modern_components.py` 中的 `THEME_COLORS` 字典来自定义配色方案。

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License

## 相机显示问题修复

如果在使用相机预览功能时，实时图像区域显示黑屏而非相机画面，可以通过以下步骤修复：

1. 运行 `python fix_camera_display.py` 检查并修复组件
2. 重启应用程序
3. 如果问题仍然存在，可以尝试运行 `python verify_camera_display.py` 测试基本相机功能

修复解决了以下问题：
- 相机图像创建成功但不显示在界面上的问题
- 图像组件可见性问题
- 图像更新机制问题

具体修复内容包括：
1. 为图像视图组件添加了唯一的objectName，方便查找
2. 添加了图像组件可见性检查和强制显示机制
3. 增强了图像更新方法，确保通过多种方式尝试显示图像
4. 添加了定时器机制，确保图像视图组件初始化完成