#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证相机显示修复

启动主应用程序并验证相机显示是否正常工作。
添加了自动诊断和UI监测功能。
"""

import sys
import os
import time
from loguru import logger

# 设置日志级别
logger.remove()
logger.add(sys.stderr, level="DEBUG")
logger.add("camera_display_fix.log", rotation="10 MB")

def main():
    """启动主应用程序并验证修复"""
    try:
        logger.info("开始验证相机显示修复")
        
        # 导入必要的模块
        from PyQt5.QtWidgets import QApplication, QLabel
        from PyQt5.QtGui import QPixmap
        from PyQt5.QtCore import QTimer, Qt
        from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 创建工作流编辑器
        editor = ModernWorkflowEditor()
        editor.setWindowTitle("相机显示修复验证")
        editor.resize(1200, 800)
        editor.show()
        
        # 添加诊断信息标签
        diagnostics_label = QLabel(editor)
        diagnostics_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 180);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
        """)
        diagnostics_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        diagnostics_label.resize(300, 200)
        diagnostics_label.move(20, 20)
        diagnostics_label.setText("诊断信息...\n准备验证相机显示")
        diagnostics_label.show()
        
        # 手动添加相机输入节点
        def add_camera_node():
            logger.info("添加相机输入节点")
            try:
                camera_node = editor.canvas.add_node(
                    node_id="camera_input_1",
                    node_type="input",
                    title="相机输入"
                )
                
                diagnostics_label.setText(diagnostics_label.text() + "\n添加相机节点成功")
                
                # 触发单次执行
                QTimer.singleShot(1000, run_workflow)
            except Exception as e:
                logger.error(f"添加相机节点时出错: {e}")
                diagnostics_label.setText(diagnostics_label.text() + f"\n添加相机节点失败: {e}")
        
        # 运行工作流
        def run_workflow():
            logger.info("触发工作流执行")
            try:
                editor._run_workflow_once()
                diagnostics_label.setText(diagnostics_label.text() + "\n单次执行工作流成功")
                
                # 延迟启动持续执行
                QTimer.singleShot(3000, start_continuous_run)
            except Exception as e:
                logger.error(f"执行工作流时出错: {e}")
                diagnostics_label.setText(diagnostics_label.text() + f"\n执行工作流失败: {e}")
        
        # 启动持续执行
        def start_continuous_run():
            logger.info("启动持续执行")
            try:
                editor._toggle_continuous_run()
                diagnostics_label.setText(diagnostics_label.text() + "\n启动持续执行成功")
                
                # 设置定时器检查显示
                QTimer.singleShot(2000, check_display)
            except Exception as e:
                logger.error(f"启动持续执行时出错: {e}")
                diagnostics_label.setText(diagnostics_label.text() + f"\n启动持续执行失败: {e}")
        
        # 检查显示状态
        def check_display():
            logger.info("检查图像显示状态")
            try:
                if hasattr(editor, 'image_view'):
                    visible = editor.image_view.isVisible()
                    has_pixmap = editor.image_view.pixmap() is not None
                    if has_pixmap:
                        pixmap_size = f"{editor.image_view.pixmap().width()}x{editor.image_view.pixmap().height()}"
                    else:
                        pixmap_size = "无"
                        
                    status = f"""
图像视图状态:
- 是否可见: {"是" if visible else "否"}
- 是否有图像: {"是" if has_pixmap else "否"}
- 图像尺寸: {pixmap_size}
"""
                    diagnostics_label.setText(status)
                    
                    if not visible or not has_pixmap:
                        # 尝试强制修复
                        logger.warning("图像显示异常，尝试强制修复")
                        if not visible:
                            editor.image_view.show()
                        if hasattr(editor, 'original_pixmap') and not editor.original_pixmap.isNull():
                            editor._force_refresh_image()
                        
                        # 再次检查
                        QTimer.singleShot(1000, check_display)
                    else:
                        logger.info(f"图像显示正常，尺寸: {pixmap_size}")
                        diagnostics_label.setText(status + "\n图像显示正常!")
                        
                        # 截图保存测试
                        if hasattr(editor, 'image_view') and editor.image_view.pixmap():
                            screenshot = editor.image_view.pixmap()
                            screenshot.save("camera_display_success.png")
                            logger.info("已保存成功截图")
                else:
                    logger.error("未找到图像视图组件")
                    diagnostics_label.setText("错误: 未找到图像视图组件")
            except Exception as e:
                logger.error(f"检查图像显示时出错: {e}")
                diagnostics_label.setText(f"检查图像出错: {e}")
        
        # 使用定时器延迟添加节点 (确保UI已完全加载)
        QTimer.singleShot(500, add_camera_node)
        
        # 运行应用程序
        return app.exec_()
    
    except Exception as e:
        logger.error(f"验证过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main()) 